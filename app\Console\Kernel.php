<?php

namespace App\Console;

use App\Jobs\CalculateWorkHoursJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */

    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('course:updatestatus')->daily();
        $schedule->command('student:updatestatus')->daily();
        $schedule->command('calculate:salaries')->monthlyOn(1, '06:00');
        $schedule->command('cashout:salaries')->monthlyOn(3, '06:00');
        $schedule->command('calculate:monthly-profits')->monthlyOn(4, '06:00');
        $schedule->call(function () {
            CalculateWorkHoursJob::dispatch()->onQueue('work_hours_queue');
        })->dailyAt('23:59');

        $schedule->command('queue:work --queue=mail_queue --once')->everyMinute();
        $schedule->command('queue:work --queue=work_hours_queue --once')->daily();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
