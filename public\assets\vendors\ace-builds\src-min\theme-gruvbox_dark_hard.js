define("ace/theme/gruvbox_dark_hard",["require","exports","module","ace/lib/dom"],function(e,t,n){t.isDark=!0,t.cssClass="ace-gruvbox-dark-hard",t.cssText=".ace-gruvbox-dark-hard .ace_gutter {background: #1d2021;color: rgb(132,126,106)}.ace-gruvbox-dark-hard .ace_print-margin {width: 1px;background: #e8e8e8}.ace-gruvbox-dark-hard {background-color: #1d2021;color: rgba(235, 219, 178, 0.50)}.ace-gruvbox-dark-hard .ace_cursor {color: #a89984}.ace-gruvbox-dark-hard .ace_marker-layer .ace_selection {background: #3c3836}.ace-gruvbox-dark-hard.ace_multiselect .ace_selection.ace_start {box-shadow: 0 0 3px 0px #1d2021;border-radius: 2px}.ace-gruvbox-dark-hard .ace_marker-layer .ace_step {background: rgb(198, 219, 174)}.ace-gruvbox-dark-hard .ace_marker-layer .ace_bracket {margin: -1px 0 0 -1px;border: 1px solid rgba(235, 219, 178, 0.15)}.ace-gruvbox-dark-hard .ace_marker-layer .ace_active-line {background: #3c3836}.ace-gruvbox-dark-hard .ace_gutter-active-line {background-color: #3c3836}.ace-gruvbox-dark-hard .ace_marker-layer .ace_selected-word {border: 1px solid #3c3836}.ace-gruvbox-dark-hard .ace_fold {background-color: #b8bb26;border-color: rgba(235, 219, 178, 0.50)}.ace-gruvbox-dark-hard .ace_keyword {color: #fb4934}.ace-gruvbox-dark-hard .ace_keyword.ace_operator {color: #8ec07c}.ace-gruvbox-dark-hard .ace_keyword.ace_other.ace_unit {color: #b16286}.ace-gruvbox-dark-hard .ace_constant {color: #d3869b}.ace-gruvbox-dark-hard .ace_constant.ace_numeric {color: #d3869b}.ace-gruvbox-dark-hard .ace_constant.ace_character.ace_escape {color: #fb4934}.ace-gruvbox-dark-hard .ace_constant.ace_other {color: #d3869b}.ace-gruvbox-dark-hard .ace_support.ace_function {color: #8ec07c}.ace-gruvbox-dark-hard .ace_support.ace_constant {color: #d3869b}.ace-gruvbox-dark-hard .ace_support.ace_constant.ace_property-value {color: #f9f5d7}.ace-gruvbox-dark-hard .ace_support.ace_class {color: #fabd2f}.ace-gruvbox-dark-hard .ace_support.ace_type {color: #fabd2f}.ace-gruvbox-dark-hard .ace_storage {color: #fb4934}.ace-gruvbox-dark-hard .ace_invalid {color: #f9f5d7;background-color: #fb4934}.ace-gruvbox-dark-hard .ace_string {color: #b8bb26}.ace-gruvbox-dark-hard .ace_string.ace_regexp {color: #b8bb26}.ace-gruvbox-dark-hard .ace_comment {font-style: italic;color: #928374}.ace-gruvbox-dark-hard .ace_variable {color: #83a598}.ace-gruvbox-dark-hard .ace_variable.ace_language {color: #d3869b}.ace-gruvbox-dark-hard .ace_variable.ace_parameter {color: #f9f5d7}.ace-gruvbox-dark-hard .ace_meta.ace_tag {color: #f9f5d7}.ace-gruvbox-dark-hard .ace_entity.ace_other.ace_attribute-name {color: #fabd2f}.ace-gruvbox-dark-hard .ace_entity.ace_name.ace_function {color: #b8bb26}.ace-gruvbox-dark-hard .ace_entity.ace_name.ace_tag {color: #83a598}.ace-gruvbox-dark-hard .ace_markup.ace_heading {color: #b8bb26}.ace-gruvbox-dark-hard .ace_markup.ace_list {color: #83a598}";var r=e("../lib/dom");r.importCssString(t.cssText,t.cssClass)});                (function() {
                    window.require(["ace/theme/gruvbox_dark_hard"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            