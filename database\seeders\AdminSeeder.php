<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Admin::create([
            'name' => '<PERSON><PERSON><PERSON>',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'profile_img' => 'admin.jpg',
            'role' => 'super_admin'
        ]);
        Admin::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'profile_img' => 'admin.jpg',
            'role' => 'super_admin'
        ]);
        Admin::create([
            'name' => 'He<PERSON> Hamdy',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'profile_img' => 'admin.jpg',
            'role' => 'admin'
        ]);
        Admin::create([
            'name' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'profile_img' => 'admin.jpg',
            'role' => 'staff'
        ]);
        Admin::create([
            'name' => 'Nourhan Elswerky',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'profile_img' => 'admin.jpg',
            'role' => 'staff'
        ]);
    }
}
