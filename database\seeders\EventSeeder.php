<?php

namespace Database\Seeders;

use App\Models\Event;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Event::create([
            'id' => 1,
            'title' => 'First Graduation Party',
            'description' => "celebrating a milestone moment at our first graduation party! An evening of joy, reflection, and achievement, marking the beginning of new journeys for our graduates, This graduation party isn't just an ending, it's a beautiful beginning.",
            'date' => '2023-08-15',
            'image' => 'e1.jpg',
        ]);
        Event::create([
            'id' => 2,
            'title' => 'Art WorkShop',
            'description' => 'Unlock your imagination and embrace the world of wearable art at our exclusive Art Workshop!, Join our workshop to paint your unique designs on t-shirts and unleash your creativity!, Don\'t miss this opportunity to express yourself and join us for an unforgettable journey of artistic exploration.',
            'date' => '2023-8-27',
            'image' => 'e2.jpg',
        ]);
        Event::create([
            'id' => 3,
            'title' => 'Winter Camp',
            'description' => 'a fun and educational program designed to introduce children to the world of programming during their winter break. The camp offers a variety of hands-on activities, workshops, and coding challenges. Through the camp, children not only learn valuable coding skills but also develop problem-solving abilities, creativity, and teamwork in a supportive and engaging environment',
            'date' => '2024-01-14',
            'image' => 'e3.jpg',
        ]);
        Event::create([
            'id' => 4,
            'title' => 'PMS Camp',
            'description' => 'PMS camp is a camp for enjoyable coding activities, promoting problem-solving, creativity, and teamwork. Through hands-on workshops and challenges, children learn valuable programming skills in a supportive and engaging atmosphere, enhancing their educational experience while having fun.',
            'date' => '2024-01-15',
            'image' => 'e5.jpg',
        ]);
        Event::create([
            'id' => 5,
            'title' => 'Ramadan Night',
            'description' => 'A special Ramadan Night at our Kids\' Academy! Enjoy fun activities, storytelling, and crafts, all themed around the spirit of Ramadan. Open to all children, Join us again in future festivities at the academy, where the magic of Ramadan continues to illuminate our hearts and minds with its timeless charm.',
            'date' => '2024-03-25',
            'image' => 'e4.jpg',
        ]);

    }
}
