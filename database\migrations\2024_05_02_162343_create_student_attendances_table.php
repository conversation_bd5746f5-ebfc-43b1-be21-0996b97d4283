<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('session_cycle_id')->nullable()->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('user_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->boolean('attendance')->default(0);
            $table->text('comment')->nullable();
            // $table->text('student_comment')->nullable();
            // $table->timestamp('feedback_sent_at')->nullable();
            // $table->enum('ins_comment_status',['pending','responded'])->default('pending');
            // $table->enum('std_comment_status',['pending','responded'])->default('pending');
            $table->timestamps();
            $table->unique(['session_cycle_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_attendances');
    }
};
