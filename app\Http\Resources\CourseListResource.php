<?php

namespace App\Http\Resources;

use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $prereq = json_decode($this->prereq, true);
        $prereqIds = [];
        if (is_array($prereq)) {
            $prereqIds = $prereq;
        } elseif (is_int($prereq)) {
            $prereqIds = [$prereq];
        }
        $prereqCourses = Course::whereIn('id', $prereqIds)->pluck('name')->toArray();
        return [
            'id' => $this->id,
            'name' => $this->name,
            'commercial_name' => $this->comm_name,
            'price' => $this->price,
            'track' => $this->track->name,
            'start_age' => $this->start_age,
            'end_age' => $this->end_age,
            'duration_hours' => $this->hours,
            'levels_number' => $this->levels,
            'sessions_number' => $this->sessions,
            'months_number' => $this->months,
            'prerequests_courses' => $prereqCourses,
            'short_desc' => $this->short_desc,
            'long_desc' => $this->long_desc,
            'exp_level' => $this->exp_level,
            'availability' => $this->availability,
            'priority' => $this->priority,
            'image' => asset('https://admin.mtechsquare.com/images/courses/' . $this->image_path),
        ];
    }
}
