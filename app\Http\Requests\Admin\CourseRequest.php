<?php

namespace App\Http\Requests\Admin;

use App\Rules\UniqueCaseInsensitive;
use Illuminate\Foundation\Http\FormRequest;

class CourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'min:3', new UniqueCaseInsensitive('courses', 'name')],
            'comm_name' => 'required|min:3|unique:courses,comm_name',
            'start_age' => 'required|numeric|between:5,18',
            'end_age' => 'required|numeric|between:5,18|gt:start_age|different:start_age',
            'levels' => 'required|numeric|min:0',
            'months' => 'required|numeric|min:0|gt:levels',
            'sessions' => 'required|numeric|min:0|gt:months',
            'hours' => 'required|numeric|min:0|gt:sessions',
            'prereq' => 'array',
            'prereq.*' => 'exists:courses,id',
            'short_desc' => [
                'required',
                'string',
                'min:5',
                'max:255',
                function ($attribute, $value, $fail) {
                    if (strlen($value) > strlen($this->input('long_desc'))) {
                        $fail('The Short description must not be longer than the long description.');
                    }
                }
            ],
            'long_desc' => 'required|string|min:5',
            'exp_level' => 'required|in:beginner,intermediate,advanced',
            'availability' => 'required|boolean',
            'image_path' => 'image|mimes:jpg,png,jpeg,gif,svg',
            'track_id' => 'required|exists:tracks,id',
            'price' => 'required|numeric|min:0',
            'has_material' => 'required|boolean',
        ];
    }
}
