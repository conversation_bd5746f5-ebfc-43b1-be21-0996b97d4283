<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Temporarily rename the existing column
        Schema::table('instructor_salaries', function (Blueprint $table) {
            $table->renameColumn('work_hours', 'work_hours_old');
        });

        // Step 2: Add a new decimal column with the correct precision
        Schema::table('instructor_salaries', function (Blueprint $table) {
            $table->decimal('work_hours', 8, 2)->nullable()->default(0);
        });

        // Step 3: Copy old integer values to the new decimal column
        DB::statement('UPDATE instructor_salaries SET work_hours = work_hours_old');

        // Step 4: Drop the old column
        Schema::table('instructor_salaries', function (Blueprint $table) {
            $table->dropColumn('work_hours_old');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Rollback: Convert work_hours back to integer
        Schema::table('instructor_salaries', function (Blueprint $table) {
            $table->renameColumn('work_hours', 'work_hours_old');
        });

        Schema::table('instructor_salaries', function (Blueprint $table) {
            $table->integer('work_hours')->nullable()->default(0);
        });

        DB::statement('UPDATE instructor_salaries SET work_hours = CAST(work_hours_old AS SIGNED)');

        Schema::table('instructor_salaries', function (Blueprint $table) {
            $table->dropColumn('work_hours_old');
        });
    }
};
