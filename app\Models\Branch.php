<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Branch extends Model
{
    use HasFactory;

    public function admins()
    {
        return $this->belongsToMany(Admin::class, 'admin_branch');
    }

    public function balances()
    {
        return $this->hasMany(Balance::class);
    }

    public function cash_ins()
    {
        return $this->hasMany(CashIn::class);
    }

    public function cash_outs()
    {
        return $this->hasMany(CashOut::class);
    }

    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function instructors()
    {
        return $this->hasMany(Instructor::class);
    }

    public function events()
    {
        return $this->hasMany(Event::class);
    }

    public function course_cucycles()
    {
        return $this->hasMany(CourseCycle::class);
    }

    public function enroll_forms()
    {
        return $this->hasMany(EnrollForm::class);
    }

    public function profits()
    {
        return $this->hasMany(Profit::class);
    }

    public function staff_members()
    {
        return $this->hasMany(Staff::class);
    }
}
