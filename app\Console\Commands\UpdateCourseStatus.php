<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\CourseCycle;
use Carbon\Carbon;

class UpdateCourseStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'course:updatestatus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update course statuses based on current date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = Carbon::today();

        CourseCycle::where('start_date', '>', $today)
            ->where('course_status', '!=', 'on_hold')
            ->update(['course_status' => 'not_started']);

        CourseCycle::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->where('course_status', '!=', 'on_hold')
            ->update(['course_status' => 'in_progress']);

        CourseCycle::where('end_date', '<', $today)
            ->where('course_status', '!=', 'on_hold')
            ->update(['course_status' => 'completed']);
    }
}
