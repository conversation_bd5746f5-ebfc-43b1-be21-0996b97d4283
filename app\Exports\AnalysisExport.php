<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Collection;

class AnalysisExport implements FromCollection, WithHeadings
{
    protected $newStudentsData;
    protected $newEnrollmentsData;
    protected $newGroupsData;
    protected $studentsData;
    protected $enrollmentsData;
    protected $groupsData;

    public function __construct($newStudentsData, $newEnrollmentsData, $newGroupsData, $studentsData, $enrollmentsData, $groupsData)
    {
        $this->newStudentsData = $this->ensureArray($newStudentsData);
        $this->newEnrollmentsData = $this->ensureArray($newEnrollmentsData);
        $this->newGroupsData = $this->ensureArray($newGroupsData);
        $this->studentsData = $this->ensureArray($studentsData);
        $this->enrollmentsData = $this->ensureArray($enrollmentsData);
        $this->groupsData = $this->ensureArray($groupsData);
    }

    private function ensureArray($data)
    {        
        if (is_string($data)) {
            return json_decode($data, true);
        }
        return $data;
    }

    public function collection()
    {        
        $exportData = [];
        foreach ($this->studentsData as $year => $months) {
            foreach ($months as $month => $value) {
                $exportData[] = [
                    'year' => $year,
                    'month' => $month,
                    'total_students' => $this->studentsData[$year][$month] ?? 0,
                    'new_students' => $this->newStudentsData[$year][$month] ?? 0,
                    'total_enrollments' => $this->enrollmentsData[$year][$month] ?? 0,
                    'new_enrollments' => $this->newEnrollmentsData[$year][$month] ?? 0,
                    'total_groups' => $this->groupsData[$year][$month] ?? 0,
                    'new_groups' => $this->newGroupsData[$year][$month] ?? 0,
                ];
            }
        }

        return new Collection($exportData);
    }

    public function headings(): array
    {
        return [
            'Year', 'Month', 'Total Students', 'New Students', 'Total Enrollments', 'New Enrollments', 'Total Groups', 'New Groups'
        ];
    }
}


