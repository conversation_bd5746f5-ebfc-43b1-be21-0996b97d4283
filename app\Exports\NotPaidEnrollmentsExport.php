<?php

namespace App\Exports;

use App\Models\Enrollment;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class NotPaidEnrollmentsExport implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    private $rowNumber = 1;
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return Enrollment::where(function ($query) {
            $query->where('course_payment_status', '!=', 'full')
                ->orWhere('material_payment_status', '!=', 'full');
        })
            ->with(['user', 'course_cycle.level.course'])
            ->get()
            ->filter(function ($enrollment) {
                $courseRemaining = $enrollment->courseRemaining;
                $materialRemaining = $enrollment->course_cycle->level->course->has_material
                    ? $enrollment->materialRemaining
                    : null;

                return $courseRemaining > 0 || ($materialRemaining !== null && $materialRemaining > 0);
            });
    }

    /**
     * Headings for the Excel file.
     */
    public function headings(): array
    {
        return [
            '#',
            'Student',
            'Course',
            'Course Payment Amount',
            'Material Payment Amount',
            'Course Price',
            'Discount',
            'Price After Discount',
            'Material Price',
            'Remaining Course Amount',
            'Remaining Material Amount',
        ];
    }

    /**
     * Map each enrollment to an array of data for the export.
     */
    public function map($enrollment): array
    {
        $row=$this->rowNumber++; 
        $discountValue = $enrollment->discount_value ?? '--';
        $priceAfterDiscount = $enrollment->course_cycle->level->course_price - ($enrollment->discount_value ?? 0);
        $materialRemaining = $enrollment->course_cycle->level->course->has_material
            ? $enrollment->materialRemaining
            : 'N/A';

        return [
            $row,
            $enrollment->user->name ?? 'None',
            $enrollment->course_cycle->level->course->name . ' - ' . $enrollment->course_cycle->level->name,
            $enrollment->course_payment_amount,
            $enrollment->material_payment_amount ?? 'N/A',
            $enrollment->course_cycle->level->course_price,
            $discountValue,
            $priceAfterDiscount,
            $enrollment->course_cycle->level->material_price ?? 'N/A',
            $enrollment->courseRemaining,
            $materialRemaining,
        ];
    }

    /**
     * Sheet title for the Excel file.
     */
    public function title(): string
    {
        return 'Not-paid-Enrollments ' . now()->format('M Y');
    }
}
