<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SessionMaterial extends Model
{
    use HasFactory;
    public function session()
    {
        return $this->belongsTo(Session::class);
    }

    public function session_cycle()
    {
        return $this->belongsTo(SessionCycle::class, 'session_id', 'session_id');
    }
}
