<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class StudentCreatedMail extends Mailable 
{
    use Queueable, SerializesModels;
    public $student;
    public $generatedPassword;
    /**
     * Create a new message instance.
     */
    public function __construct($student, $password)
    {
        $this->student = $student;
        $this->generatedPassword = $password;
    }


    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Welcome to TechnoSquare Academy',
        );
    }
    public function build()
    {
        return $this->markdown('emails.student.created')
            ->with([
                'student' => $this->student,
                'password' =>  $this->generatedPassword 
            ]);
    }

    /**
     * Get the message content definition.
     */
    // public function content(): Content
    // {
    //     return new Content(
    //         markdown: 'emails.student.created',
    //     );
    // }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
