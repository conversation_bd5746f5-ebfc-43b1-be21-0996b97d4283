<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseGroupResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'group_name' => $this->name,
            'course_name' => $this->level->course->name,
            'level_name' => $this->level->name,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'day' => $this->day,
            'time' => $this->time,
            'course_status' => $this->course_status,
            'season' => $this->cycle->name,
            'students_count' => $this->enrollments->count()
        ];
    }
}
