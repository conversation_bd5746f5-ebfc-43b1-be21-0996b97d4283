<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\InstructorSalary;
use Illuminate\Http\Request;

class InstructorSalaryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        $data = $request->validate([
            'salaries' => 'required|array',
            'salaries.*.bonus' => 'nullable|numeric',
            'salaries.*.media_bonus' => 'nullable|numeric',
        ]);
    
        foreach ($data['salaries'] as $id => $salaryData) {
            $salary = InstructorSalary::findOrFail($id);
            $salary->bonus = $salaryData['bonus'] ?? 0;
            $salary->media_bonus = $salaryData['media_bonus'] ?? 0;
            $salary->save();
        }
    
        return redirect()->back()->with('update', 'Salaries updated successfully');
    }
    

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

}