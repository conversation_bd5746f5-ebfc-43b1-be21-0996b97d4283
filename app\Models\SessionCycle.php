<?php

namespace App\Models;

use App\Services\BranchManager;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SessionCycle extends Model
{
    use HasFactory;
    protected static function boot()
    {
        parent::boot();

        static::updated(function ($sessionCycle) {
            $courseCycle = $sessionCycle->courseCycle;

            $firstSessionDate = $courseCycle->sessionCycles()->orderBy('date')->first()->date;
            $lastSessionDate = $courseCycle->sessionCycles()->orderBy('date', 'desc')->first()->date;

            if ($courseCycle->start_date != $firstSessionDate || $courseCycle->end_date != $lastSessionDate) {
                $courseCycle->start_date = $firstSessionDate;
                $courseCycle->end_date = $lastSessionDate;
                $courseCycle->save();
            }
            $nextSessions = $courseCycle->sessionCycles()
                ->where('date', '>', $sessionCycle->date)
                ->orderBy('date')
                ->get();

            foreach ($nextSessions as $index => $nextSession) {
                $nextSession->date = Carbon::parse($sessionCycle->date)->copy()->addDays(($index + 1) * 7);
                $nextSession->save();
            }
        });
    }

    public function scopeForCurrentBranch($query)
    {
        $branchManager = app(BranchManager::class);

        if ($branchManager->isBranchContextSet()) {
            $branchId = $branchManager->getBranchId();

            $query->whereHas('courseCycle', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        }

        return $query;
    }
    public function student_attendances()
    {
        return $this->hasMany(StudentAttendance::class);
    }
    public function instructor_attendances()
    {
        return $this->hasMany(InstructorAttendance::class);
    }
    public function session()
    {
        return $this->belongsTo(Session::class);
    }
    public function courseCycle()
    {
        return $this->belongsTo(CourseCycle::class);
    }
    public function student_feedbacks()
    {
        return $this->hasMany(StudentFeedback::class);
    }
}
