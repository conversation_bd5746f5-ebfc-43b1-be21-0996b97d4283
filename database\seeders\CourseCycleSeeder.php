<?php

namespace Database\Seeders;

use App\Models\CourseCycle;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CourseCycleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        CourseCycle::create([
            'id' => 1,
            'name' => 'Scratch-Mon-G1',
            'start_date' => '2024-5-15',
            'end_date' => '2024-7-15',
            'day' => 'Monday',
            'time' => '4pm',
            'room' => 'room1',
            'instructor_id' => '1',
            'level_id' => '1',
            'cycle_id' => '1',
            'type' => 'offline',
            'place' => 'academy',
        ]);
        CourseCycle::create([
            'id' => 2,
            'name' => 'Unity-Wed-G1',
            'start_date' => '2024-6-20',
            'end_date' => '2024-8-20',
            'day' => 'Wedensday',
            'time' => '6pm',
            'room' => 'room1',
            'instructor_id' => '1',
            'level_id' => '2',
            'cycle_id' => '2',
            'type' => 'online',
        ]);
        CourseCycle::create([
            'id' => 3,
            'name' => 'Prime-Mon-G1',
            'start_date' => '2024-5-15',
            'end_date' => '2024-7-15',
            'day' => 'Monday',
            'time' => '12pm',
            'instructor_id' => '1',
            'level_id' => '3',
            'cycle_id' => '1',
            'type' => 'offline',
            'place' => 'pms',
        ]);
        CourseCycle::create([
            'id' => 4,
            'name' => 'Essential-Mon-G1',
            'start_date' => '2024-5-15',
            'end_date' => '2024-7-15',
            'day' => 'Monday',
            'time' => '2pm',
            'room' => 'room1',
            'instructor_id' => '1',
            'level_id' => '3',
            'cycle_id' => '1',
            'type' => 'offline',
            'place' => 'academy',
        ]);
    }
}
