<?php

namespace App\Exports;

use App\Models\SessionCycle;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Carbon\Carbon;


class CommentsExport implements FromCollection, WithHeadings, WithTitle
{
    protected $startOfMonth;
    protected $endOfMonth;

    public function __construct($startOfMonth, $endOfMonth)
    {
        $this->startOfMonth = $startOfMonth;
        $this->endOfMonth = $endOfMonth;
    }
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $sessions = SessionCycle::whereNotNull('comment')
            ->whereBetween('date', [$this->startOfMonth, $this->endOfMonth])
            ->with('courseCycle.instructor', 'courseCycle.level.course')
            ->orderBy('date', 'desc')
            ->get();

        return $sessions->map(function ($session, $index) {
            return [
                'index' => $index + 1,
                'course' => $session->courseCycle->level->course->name ?? 'N/A',
                'session' => $session->session->name ?? 'N/A',
                'date' => $session->date,
                'instructor' => $session->courseCycle->instructor->name ?? 'N/A',
                'comment' => $session->comment,
            ];
        });
    }
    public function headings(): array
    {
        return [
            '#',
            'Course',
            'Session',
            'Session Date',
            'Instructor',
            'Comment',
        ];
    }
    public function title(): string
    {
        $date = Carbon::parse($this->startOfMonth);
        return 'Instructor-Comments '.$date->format('M Y'); 
    }
}
