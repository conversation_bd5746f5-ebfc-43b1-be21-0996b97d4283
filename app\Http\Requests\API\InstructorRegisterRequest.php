<?php

namespace App\Http\Requests\API;
use App\Http\Requests\API\FormRequest;


class InstructorRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'=>'required|string',
            'email'=>'required|string|email|unique:instructors',
            'password'=>'required|min:8',
            'phone'=> 'required|unique:instructors'
        ];
    }
}
