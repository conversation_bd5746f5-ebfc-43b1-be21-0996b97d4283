<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateSessionCycleDatesRequest;
use App\Models\SessionCycle;
use Illuminate\Http\Request;
use Carbon\Carbon;


class SessionCycleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(SessionCycle $sessionCycle)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SessionCycle $sessionCycle)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSessionCycleDatesRequest $request, SessionCycle $sessionCycle)
    {        
        // Update the current session date
        $newDate = $request->input('date_' . $sessionCycle->id);
        $sessionCycle->date = $newDate;
        $sessionCycle->save();
    
        // Automatically update subsequent session dates
        $courseCycle = $sessionCycle->courseCycle;
        $nextSessions = $courseCycle->sessionCycles()
                                    ->where('date', '>=', $sessionCycle->date)
                                    ->orderBy('date')
                                    ->get();
    
        foreach ($nextSessions as $index => $nextSession) {
            $nextSession->date = Carbon::parse($sessionCycle->date)->addDays($index  * 7);
            $nextSession->save();
        }
    
        return redirect()->back()->with('update', $sessionCycle->session->name . ' date updated successfully.');
    }     

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SessionCycle $sessionCycle)
    {
        //
    }
}
