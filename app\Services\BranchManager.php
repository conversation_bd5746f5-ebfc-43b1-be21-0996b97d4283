<?php

namespace App\Services;

use App\Models\Branch; // Make sure Branch model namespace is correct

class BranchManager // Renamed class
{
    public ?Branch $currentBranch = null;

    /**
     * Set the currently active branch for this request.
     */
    public function setBranch(?Branch $branch): void
    {
        $this->currentBranch = $branch;
        // Optional: Set config or other global context if needed elsewhere
        // config(['app.current_branch_id' => $branch?->id]);
    }

    /**
     * Get the full Branch model for the current context.
     */
    public function getBranch(): ?Branch
    {
        return $this->currentBranch;
    }

    /**
     * Get the ID of the currently active branch.
     */
    public function getBranchId(): ?int
    {
        return $this->currentBranch?->id;
    }

    /**
     * Check if a branch context has been successfully set for this request.
     */
    public function isBranchContextSet(): bool
    {
        return $this->currentBranch !== null;
    }
}