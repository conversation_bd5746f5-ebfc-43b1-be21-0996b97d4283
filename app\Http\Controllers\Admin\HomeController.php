<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Models\Course;
use App\Models\CourseCycle;
use App\Models\EnrollForm;
use App\Models\InstructorSalary;
use App\Models\SessionCycle;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\Instructor;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        ///SECTION -  Tables Filtration by Month
        //Selected Month and year
        $monthYear = $request->query('month');
        if ($monthYear) {
            [$year, $month] = explode('-', $monthYear);
            $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth();
            $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth();
        } else {
            $startOfMonth = Carbon::now()->startOfMonth();
            $endOfMonth = Carbon::now()->endOfMonth();
        }

        // Filter sessions by the selected month
        $sessions = SessionCycle::forCurrentBranch()
            ->whereNotNull('comment')
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->with('courseCycle.instructor')
            ->orderBy('date', 'desc')
            ->paginate(5);

        // Filter grades by selected month and name
        $studentNameFilter = $request->query('student_name', '');
        $courseCycleFilter = $request->query('course_cycle_name', '');
        $lastSessions = DB::table('session_cycles as sc')
            ->select('sc.course_cycle_id', DB::raw('MAX(sc.date) as last_session_date'))
            ->groupBy('sc.course_cycle_id');

        // Main query to filter enrollments by last session date and other filters
        $gradesQuery = Enrollment::forCurrentBranch()
            ->joinSub($lastSessions, 'last_sessions', function ($join) {
                $join->on('enrollments.course_cycle_id', '=', 'last_sessions.course_cycle_id');
            })
            ->join('session_cycles', function ($join) {
                $join->on('session_cycles.course_cycle_id', '=', 'last_sessions.course_cycle_id')
                    ->on('session_cycles.date', '=', 'last_sessions.last_session_date');
            })
            ->whereBetween('last_sessions.last_session_date', [$startOfMonth, $endOfMonth])
            ->when($studentNameFilter, function ($query) use ($studentNameFilter) {
                return $query->whereHas('user', function ($query) use ($studentNameFilter) {
                    $query->where('name', 'like', "%$studentNameFilter%");
                });
            })
            ->when($courseCycleFilter, function ($query) use ($courseCycleFilter) {
                return $query->whereHas('course_cycle', function ($query) use ($courseCycleFilter) {
                    $query->where('name', 'like', "%$courseCycleFilter%");
                });
            })
            ->with(['user', 'course_cycle'])
            ->select('enrollments.*', 'session_cycles.date as last_session_date')
            ->paginate(5);


        // Filter instructor salaries by selected month
        $instructorNameFilter = $request->query('instructor_name', '');
        $salariesQuery = InstructorSalary::forCurrentBranch()
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->when($instructorNameFilter, function ($query) use ($instructorNameFilter) {
                return $query->whereHas('instructor', function ($query) use ($instructorNameFilter) {
                    $query->where('name', 'like', "%$instructorNameFilter%");
                });
            })
            ->with(['instructor'])
            ->paginate(5);


        //SECTION - Charts Queries
        $startOfThisMonth = Carbon::now()->startOfMonth();
        $endOfThisMonth = Carbon::now()->endOfMonth();
        $enrollmentsThisMonth = Enrollment::forCurrentBranch()
            ->whereBetween('created_at', [$startOfThisMonth, $endOfThisMonth])->count();
        $enrollmentRequests = EnrollForm::whereBetween('created_at', [$startOfThisMonth, $endOfThisMonth])->count();
        $activeStudents = User::where('status', 1)->count();
        $newGroups = CourseCycle::whereBetween('start_date', [$startOfThisMonth, $endOfThisMonth])->count();
        $currentGroups = CourseCycle::where('course_status', 'in_progress')->count();


        //Month and year data analysis charts
        $years = [Carbon::now()->subYear()->year, Carbon::now()->year];

        $currentYear = Carbon::now()->year;
        $currentMonth = Carbon::now()->month;

        foreach ($years as $year) {
            $yearlyStart = Carbon::create($year, 1, 1)->startOfYear();
            $yearlyEnd = $yearlyStart->copy()->endOfYear();
            for ($month = 1; $month <= 12; $month++) {
                if ($year === $currentYear && $month > $currentMonth) {
                    continue;
                }

                $startOfMonth = $yearlyStart->copy()->month($month)->startOfMonth();
                $endOfMonth = $startOfMonth->copy()->endOfMonth();
                // $endMonth = Carbon::create($year, $month, 1)->endOfMonth();
                $studentsData[$year][$month] = User::where('created_at', '<=', $endOfMonth)->count();
                $enrollmentsData[$year][$month] = Enrollment::forCurrentBranch()
                    ->where('created_at', '<=', $endOfMonth)->count();
                $groupsData[$year][$month] = CourseCycle::where('created_at', '<=', $endOfMonth)->count();
                $newStudentsData[$year][$month] = User::whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();
                $newEnrollmentsData[$year][$month] = Enrollment::forCurrentBranch()
                    ->whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();
                $newGroupsData[$year][$month] = CourseCycle::whereBetween('created_at', [$startOfMonth, $endOfMonth])->count();
            }
        }

        $publishedCourses = Course::where('availability', 1)->count();
        $notPublishedCourses = Course::where('availability', 0)->count();
        $currentInstructors = Instructor::where('status', 1)->count();
        $pendingContacts = Contact::where('status', 'pending')->count();
        $respondedContacts = Contact::where('status', 'responded')->count();
        $pendingEnrollments = EnrollForm::where('request_status', 'pending')->count();
        $acceptedEnrollments = EnrollForm::where('request_status', 'accepted')->count();
        $rejectedEnrollments = EnrollForm::where('request_status', 'rejected')->count();

        return view('index', compact(
            'enrollmentsThisMonth',
            'enrollmentRequests',
            'activeStudents',
            'newGroups',
            'currentGroups',
            'sessions',
            'gradesQuery',
            'salariesQuery',
            'years',
            'newStudentsData',
            'newEnrollmentsData',
            'newGroupsData',
            'studentsData',
            'enrollmentsData',
            'groupsData',
            'publishedCourses',
            'notPublishedCourses',
            'currentInstructors',
            'pendingContacts',
            'respondedContacts',
            'pendingEnrollments',
            'acceptedEnrollments',
            'rejectedEnrollments',
        ));
    }

}
