<?php

namespace App\Http\Middleware;

use App\Models\CourseCycle;
use App\Models\Enrollment;
use App\Models\SessionCycle;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckInstructorAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {
        if (Auth::guard('instructor')->check()) {
            $user = auth('instructor')->user();
            $course_id = null;

            if ($request->route()->hasParameter('course_cycle_id')) {
                $course_id = $request->route('course_cycle_id');
            } elseif ($request->route()->hasParameter('session_cycle_id')) {
                $session_id = $request->route('session_cycle_id');
                $session = SessionCycle::findOrFail($session_id);
                $course_id = $session->course_cycle_id;
            }

            if (!$course_id) {
                return response()->json(['error' => 'Invalid request.'], 400);
            }

            $group = CourseCycle::where('id', $course_id)
                ->where('instructor_id', $user->id)
                ->first();

            if (!$group) {
                return response()->json(['error' => 'Unauthorized: You do not have permission to access this course group.'], 403);
            }

            return $next($request);

        } elseif (Auth::guard('student')->check()) {
            $user = auth('student')->user();
            $course_id = null;

            if ($request->route()->hasParameter('course_cycle_id')) {
                $course_id = $request->route('course_cycle_id');
            } elseif ($request->route()->hasParameter('session_cycle_id')) {
                $session_id = $request->route('session_cycle_id');
                $session = SessionCycle::findOrFail($session_id);
                $course_id = $session->course_cycle_id;
            }

            if (!$course_id) {
                return response()->json(['error' => 'Invalid request.'], 400);
            }

            $group = Enrollment::where('course_cycle_id', $course_id)
                ->where('user_id', $user->id)
                ->first();

            if (!$group) {
                return response()->json(['error' => 'Unauthorized: You do not have permission to access this course group.'], 403);
            }

            return $next($request);

        } else {
            return response()->json(['message' => 'Unauthenticated.'], 401);
        }
    }

}
