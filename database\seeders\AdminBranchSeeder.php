<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;


class AdminBranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('admin_branch')->insert([
            ['admin_id' => 1, 'branch_id' => 1],
            ['admin_id' => 1, 'branch_id' => 2],
            ['admin_id' => 2, 'branch_id' => 1],
            ['admin_id' => 2, 'branch_id' => 2],
            ['admin_id' => 3, 'branch_id' => 1],    
            ['admin_id' => 3, 'branch_id' => 2],
            ['admin_id' => 4, 'branch_id' => 1],
            ['admin_id' => 4, 'branch_id' => 2],
            ['admin_id' => 5, 'branch_id' => 1],
            ['admin_id' => 6, 'branch_id' => 1],
            ['admin_id' => 8, 'branch_id' => 1],
            ['admin_id' => 8, 'branch_id' => 2],
            ['admin_id' => 9, 'branch_id' => 2],
        ]);
    }
}
