<?php

namespace App\Http\Requests\Admin;

use App\Models\CourseCycle;
use App\Models\Cycle;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;
use Illuminate\Support\Facades\DB;

class CourseCycleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */

    public function rules(): array
    {
        $startDate = $this->input('start_date');
        $endDate = $this->input('end_date');
        return [
            'name' => 'required',
            // 'start_date' => 'required|date|after_or_equal:today',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'day' => ['required'],
            'time' => ['required'],
            'room' => ['required_if:place,academy', 'in:room1,room2,room3'],
            'course_status' => ['required', 'in:not_started,in_progress,completed,on_hold'],
            'instructor_id' => ['required', 'exists:instructors,id'],
            'level_id' => ['required', 'exists:levels,id'],
            'cycle_id' => ['required', 'exists:cycles,id'],
            'type' => ['required', 'in:online,offline'],
            'place' => ['required_if:type,offline', 'in:academy,pms'],
        ];
    }
    protected function withValidator(Validator $validator)
    {
        $validator->after(function ($validator) {
            $this->validateCourseGroupDates($validator);
            $this->validateCourseCycleWithinCycleDates($validator);
            $this->validateUniqueCombination($validator);
        });
    }

    protected function validateCourseGroupDates($validator)
    {
        $place = $this->input('place');
        if ($place === 'academy') {
            $day = $this->input('day');
            $time = $this->input('time');
            $room = $this->input('room');
            $startDate = $this->input('start_date');

            $conflictingGroup = CourseCycle::where('day', $day)
                ->where('time', $time)
                ->where('room', $room)
                ->where('end_date', '>=', $startDate)
                ->where('course_status', '!=', 'on_hold') 
                ->first();

            if ($conflictingGroup) {
                $validator->errors()->add('start_date', 'A group with the same day, time, and room already exists within the selected period. The start date must be after the end date of this group.');
            }
        }
    }


    protected function validateCourseCycleWithinCycleDates($validator)
    {
        $startDate = $this->input('start_date');
        $cycleId = $this->input('cycle_id');
        $cycle = Cycle::find($cycleId);

        if ($cycle) {
            if (($cycle->start_date && $startDate < $cycle->start_date)) {
                $validator->errors()->add('start_date', 'The start date must be within the selected cycle\'s period.');
            }
        }
    }

    protected function validateUniqueCombination($validator)
    {
        $exists = DB::table('course_cycles')->where([
            ['start_date', $this->start_date],
            ['end_date', $this->end_date],
            ['instructor_id', $this->instructor_id],
            ['day', $this->day],
            ['time', $this->time],
        ])
        ->where('course_status', '!=', 'on_hold')
        ->exists();

        if ($exists) {
            $validator->errors()->add('unique_combination', 'A course cycle with the same combination of start date, end date, instructor, day, and time already exists.');
        }
    }

}
