<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(AdminSeeder::class);
        $this->call(TrackSeeder::class);
        $this->call(CourseSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(InstructorSeeder::class);
        $this->call(EventSeeder::class);
        $this->call(CycleSeeder::class);
        $this->call(CourseCycleSeeder::class);
        $this->call(TestimonialSeeder::class);
        $this->call(EnrollmentSeeder::class);
        $this->call(FinanceCategorySeeder::class);
        $this->call(FinanceSubCategorySeeder::class);
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
    }
}
