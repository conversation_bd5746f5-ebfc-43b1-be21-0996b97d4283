<?php

namespace App\Http\Requests\API;

use App\Http\Requests\API\FormRequest;

class ContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'=>'required|string|min:3',
            'phone' => ['required', 'digits:11', 'regex:/^(010|011|012)\d{8}$/'],
            'subject' => 'required|string',
            'message' => 'required|string',
        ];
    }
}
