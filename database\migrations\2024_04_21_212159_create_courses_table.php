<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('comm_name')->nullable()->unique();
            $table->unsignedInteger('start_age');
            $table->unsignedInteger('end_age')->nullable();
            $table->unsignedInteger('hours')->nullable();
            $table->unsignedInteger('levels')->nullable();
            $table->unsignedInteger('sessions')->nullable();
            $table->unsignedInteger('months')->nullable();
            $table->decimal('price')->nullable();
            $table->json('prereq')->nullable();
            $table->text('short_desc')->nullable();
            $table->text('long_desc')->nullable();
            $table->enum('exp_level',['beginner','intermediate','advanced']);
            $table->boolean('availability')->default(1); 
            $table->boolean('has_material')->default(0); 
            $table->unsignedInteger('priority')->default(0);
            $table->string('image_path')->nullable();
            $table->foreignId('track_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
