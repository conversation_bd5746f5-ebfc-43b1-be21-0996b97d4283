<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;
use App\Models\User;
use App\Services\BranchManager; // <-- Use the new class name

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        if ($request->expectsJson()) {
            return null;
        }

        $branchManager = app(BranchManager::class); // <-- Get BranchManager

        // If branch context is set, try to redirect to that branch's login page
        if ($branchManager->isBranchContextSet()) {
            $branchSlug = $branchManager->getBranch()->subdomain;
            $baseDomain = config('app.domain', 'techno.square.branches'); // <-- Ensure 'app.domain' is configured

            // Option A: Construct URL manually (Reliable)
            return 'https://' . $branchSlug . '.' . $baseDomain . '/';

            // Option B: Try using route name (Requires careful route definition)
            // Test if route('login') generates the correct subdomain URL in your setup
            // try {
            //    // May need temporary URL defaults set by IdentifyBranch middleware
            //    return route('login');
            // } catch (\Exception $e) {
            //    // Fallback to manual construction if route fails
            //    return 'https://' . $branchSlug . '.' . $baseDomain . '/login';
            // }
        } else {
            // Fallback for requests without branch context (e.g., main domain)
            // Redirect to the standard login route name
            return route('login');
        }
    }
}
