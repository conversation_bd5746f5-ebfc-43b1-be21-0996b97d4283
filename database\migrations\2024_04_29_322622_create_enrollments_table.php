<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enrollments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('course_cycle_id')->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->boolean('status')->default(1)->nullable();
            $table->decimal('course_payment_amount')->default(0)->nullable();
            $table->decimal('material_payment_amount')->default(0)->nullable();
            $table->enum('course_payment_status', ['none', '1month', 'full', 'down_payment'])->nullable();
            $table->enum('material_payment_status', ['none', '1month', 'full', 'down_payment'])->nullable();
            $table->enum('payment_way', ['cash', 'instapay', 'vodafone_cash', 'none'])->nullable();
            $table->text('payment_note')->nullable();
            $table->decimal('discount_value')->default(0)->nullable();
            $table->decimal('total_price')->default(0)->nullable();
            $table->enum('graduation_status', ['certified', 'not_certified']);
            $table->unsignedInteger('grade')->nullable();
            $table->string('badge')->nullable();
            $table->date('enroll_date')->nullable();
            $table->timestamps();
            $table->unique(['course_cycle_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enrollements');
    }
};
