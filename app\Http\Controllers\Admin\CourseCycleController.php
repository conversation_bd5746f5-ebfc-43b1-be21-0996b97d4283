<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CourseCycleRequest;
use App\Http\Requests\Admin\UpdateCourseCycleRequest;
use App\Models\Course;
use App\Models\CourseCycle;
use App\Models\Cycle;
use App\Models\Enrollment;
use App\Models\Instructor;
use App\Models\InstructorAttendance;
use App\Models\Level;
use App\Models\SessionCycle;
use App\Models\StudentAttendance;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Http\Request;
use Illuminate\Database\QueryException;
use Illuminate\Validation\ValidationException;

class CourseCycleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $instructorId = request()->input('instructor');
        $startDate = request()->input('start_date');
        $endDate = request()->input('end_date');
        $instructors = Instructor::all();
        $CourseCycle = CourseCycle::orderByRaw("FIELD(course_status, 'in_progress', 'not_started', 'on_hold', 'completed')")
            ->orderBy('created_at', 'desc');
        // $CourseCycle = CourseCycle::orderBy('created_at', 'desc');
        if ($search) {
            $CourseCycle = $CourseCycle
                ->whereAny(['name', 'start_date', 'room'], 'like', '%' . request()->get('search', '') . '%');
        }
        if ($instructorId) {
            $CourseCycle = $CourseCycle->where('instructor_id', $instructorId);
        }
        if ($startDate && $endDate) {
            $CourseCycle = $CourseCycle->whereBetween('start_date', [$startDate, $endDate]);
        } elseif ($startDate) {
            $CourseCycle = $CourseCycle->where('start_date', '>=', $startDate);
        } elseif ($endDate) {
            $CourseCycle = $CourseCycle->where('start_date', '<=', $endDate);
        }
        confirmDelete('Delete Course Group!', "Are you sure you want to delete this course group?");
        return view('course_cycles.index', [
            'course_cycles' => $CourseCycle->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'instructors' => $instructors,
        ]);
    }
    public function getHumatAlwatanGroups()
    {
        $search = request()->input('search');
        $instructorId = request()->input('instructor');
        $startDate = request()->input('start_date');
        $endDate = request()->input('end_date');
        $instructors = Instructor::all();
        $CourseCycle = CourseCycle::where('hezb', '=', 1)->orderBy('created_at', 'desc');
        if ($search) {
            $CourseCycle = $CourseCycle
                ->whereAny(['name', 'start_date', 'room'], 'like', '%' . request()->get('search', '') . '%');
        }
        if ($instructorId) {
            $CourseCycle = $CourseCycle->where('instructor_id', $instructorId);
        }
        if ($startDate && $endDate) {
            $CourseCycle = $CourseCycle->whereBetween('start_date', [$startDate, $endDate]);
        } elseif ($startDate) {
            $CourseCycle = $CourseCycle->where('start_date', '>=', $startDate);
        } elseif ($endDate) {
            $CourseCycle = $CourseCycle->where('start_date', '<=', $endDate);
        }
        confirmDelete('Delete Course Group!', "Are you sure you want to delete this course group?");
        return view('course_cycles.index', [
            'course_cycles' => $CourseCycle->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'instructors' => $instructors,
        ]);
    }

    public function getSchedule()
    {
        $courses = CourseCycle::with('enrollments')
            ->where('course_status', 'in_progress')
            ->get();

        $schedule = [];
        foreach ($courses as $course) {
            $day = strtolower($course->day);
            $startTime = Carbon::parse($course->time);
            $endTime = $startTime->copy()->addHours(2);
            $studentsNumber =  $course->enrollments->count();
            $schedule[$day][] = [
                'name' => $course->name,
                'instructor' => DB::table('instructors')->find($course->instructor_id)->name ?? 'N/A',
                'start' => $startTime->format('H:i'),
                'end' => $endTime->format('H:i'),
                'room' => $course->room,
                'students' => $studentsNumber,
            ];
        }

        return view('course_cycles.schedule', compact('schedule'));
    }


    public function active()
    {
        $search = request()->input('search');
        $instructorId = request()->input('instructor');
        $startDate = request()->input('start_date');
        $endDate = request()->input('end_date');
        $instructors = Instructor::all();
        $CourseCycle = CourseCycle::where('end_date', '>=', now())->orderBy('created_at', 'desc');
        if ($search) {
            $CourseCycle = $CourseCycle
                ->whereAny(['name', 'start_date', 'room'], 'like', '%' . request()->get('search', '') . '%');
        }
        if ($instructorId) {
            $CourseCycle = $CourseCycle->where('instructor_id', $instructorId);
        }
        if ($startDate && $endDate) {
            $CourseCycle = $CourseCycle->whereBetween('start_date', [$startDate, $endDate]);
        } elseif ($startDate) {
            $CourseCycle = $CourseCycle->where('start_date', '>=', $startDate);
        } elseif ($endDate) {
            $CourseCycle = $CourseCycle->where('start_date', '<=', $endDate);
        }
        confirmDelete('Delete Course Group!', "Are you sure you want to delete this course group?");
        return view('course_cycles.index', [
            'course_cycles' => $CourseCycle->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'instructors' => $instructors,
        ]);
    }
    public function getAvailableInstructors(Request $request)
    {
        $day = $request->query('day');
        $time = $request->query('time');
        $startDate = $request->query('start_date');

        $conflictingInstructors = CourseCycle::where('day', $day)
            ->where('time', $time)
            ->where('end_date', '>=', $startDate)
            ->where('course_status', '!=', 'on_hold')
            ->pluck('instructor_id');

        $availableInstructors = Instructor::whereNotIn('id', $conflictingInstructors)
            ->where('status', 1)
            ->get();

        return response()->json($availableInstructors);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function add($id)
    {
        $course_levels = Level::where('course_id', $id)->get();
        $instructors = Instructor::where('status', 1)->get();
        $course = Course::find($id);
        $cycles = Cycle::latest()->get();

        return view('course_cycles.create', compact('course_levels', 'instructors', 'id', 'cycles'), ['course' => $course->name]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CourseCycleRequest $request)
    {
        $cycle = Cycle::find($request->cycle_id);
        $level = Level::find($request->level_id);
        $levelParts = explode(' ', $level->name);
        $levelShortName = strtoupper(substr($levelParts[0], 0, 1)) . $levelParts[1];
        $courseCycleName = "{$cycle->name} - {$request->input('name')} - {$levelShortName} - {$request->input('day')} - {$request->input('time')}";
        CourseCycle::create($request->except('_token', 'name') +
            ['name' => $courseCycleName]);
        return redirect()->route('course_cycle.index')->with('add', 'Course Group Created Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(CourseCycle $courseCycle)
    {
        $enrollments = Enrollment::where('course_cycle_id', $courseCycle->id)->paginate(24);
        $sessions = SessionCycle::where('course_cycle_id', $courseCycle->id)->get();
        $sessionIds = $sessions->pluck('id')->toArray();
        $attendances = StudentAttendance::whereIn('session_cycle_id', $sessionIds)->get();
        $instructors = Instructor::where('status', 1)->get();
        $instructors_attendances = InstructorAttendance::all();
        $currentDate = now();
        $sessions->transform(function ($session) use ($currentDate) {
            $session->disabled = $session->date < $currentDate;
            return $session;
        });

        return view('course_cycles.show', compact('courseCycle', 'enrollments', 'sessions', 'attendances', 'instructors', 'instructors_attendances'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CourseCycle $course_cycle)
    {
        $course_levels = Level::where('course_id', $course_cycle->level->course->id)->get();
        $instructors = Instructor::where('status', 1)->get();
        $course_name = $course_cycle->level->course->name;
        $cycles = Cycle::latest()->get();
        $cycle = Cycle::latest('id')->first();
        return view('course_cycles.edit', compact('course_cycle', 'instructors', 'course_levels', 'course_name', 'cycle', 'cycles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCourseCycleRequest $request, CourseCycle $course_cycle)
    {
        $cycle = Cycle::find($request->cycle_id);
        $level = Level::find($request->level_id);
        $levelParts = explode(' ', $level->name);
        $levelShortName = strtoupper(substr($levelParts[0], 0, 1)) . $levelParts[1];
        // $courseCycleName = "{$cycle->name} - {$request->input('name')} - {$levelShortName} - {$request->input('day')} - {$request->input('time')}";
        // $course_cycle->update($request->except('_token', '_method'));
        // ['name' => $courseCycleName]);
        try {
            $course_cycle->update($request->except('_token', '_method'));
        } catch (QueryException $e) {
            if ($e->getCode() == 23000) { // SQLSTATE[23000] = Integrity constraint violation
                // You can return back with a validation error on a specific field
                throw ValidationException::withMessages([
                    'start_date' => 'A course group with the same instructor, day, time, and date range already exists.',
                ]);
            }

            // Re-throw if it's a different DB error
            throw $e;
        }
        return redirect()->route('course_cycle.index')->with('update', 'Course Group Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CourseCycle $courseCycle)
    {
        $courseCycle->delete();
        return redirect()->route('course_cycle.index')->with('delete', 'Course Group Deleted Successfully');
    }
}
