<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('session_cycles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_cycle_id')->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('session_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->date('date')->nullable();
            $table->text('comment')->nullable();
            $table->text('material_link')->nullable();
            $table->boolean('students_attendance')->default(0);
            $table->timestamps();
            $table->unique(['course_cycle_id', 'session_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('session_cycles');
    }
};
