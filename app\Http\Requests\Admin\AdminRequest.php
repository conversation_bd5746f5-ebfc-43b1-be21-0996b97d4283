<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class AdminRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'min:8'],
            'email' => ['required', 'email', 'unique:admins'],
            'password' => ['required', Password::min(6)],
            'phone' => ['required', 'digits:11', 'unique:admins'],
            'date_of_birth' => ['required'],
            'city' => ['required'],
            'address' => ['required'],
            'gender' => ['required'],
            'date_of_join' => ['required'],
            'job_type' => ['required'],
            'work_hours' => ['required'],
            'salary' => ['required'],
            'status' => ['required'],
            'role' => ['required'],
            'branch_id' => ['required', 'array', 'min:1'], 
            'branch_id.*' => ['exists:branches,id'],
        ];
    }
}
