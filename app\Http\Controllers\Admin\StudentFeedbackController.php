<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\StudentAttendance;
use App\Models\StudentFeedback;
use Illuminate\Http\Request;

class StudentFeedbackController extends Controller
{
    public function index()
    {
        $search = request()->input('search');
        $status = request()->input('status', 'all'); // Default to 'all'

        // Start with the base query for feedbacks
        $feedbacks = StudentFeedback::forCurrentBranch()->orderBy('created_at', 'desc');

        // Apply search filter
        if ($search) {
            $feedbacks = $feedbacks->where(function ($query) use ($search) {
                $query->where('feedback', 'like', '%' . $search . '%')
                      ->orWhere('feedback_status', 'like', '%' . $search . '%');
            });
        }

        // Apply status filter
        if ($status !== 'all') {
            $feedbacks = $feedbacks->where('feedback_status', $status);
        }

        confirmDelete('Delete Message!', "Are you sure you want to delete this message?");

        return view('students_feedback.index', [
            'feedbacks' => $feedbacks->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'status' => $status,
        ]);
    }


    public function show(StudentFeedback $feedback)
    {
        return view('students_feedback.show', compact('feedback'));
    }
    public function update(Request $request, StudentFeedback $feedback)
    {
        $feedback->feedback_status = $request->feedback_status;
        $feedback->save();
        return redirect()->back()->with('success', 'Status updated successfully!');
    }
    public function destroy(StudentFeedback $feedback)
    {
        $feedback->delete();
        return redirect()->route('students_feedback.index')->with('delete', 'Message Deleted Successfully');
    }
}
