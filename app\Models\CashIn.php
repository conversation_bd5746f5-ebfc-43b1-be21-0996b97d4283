<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use App\Scopes\BranchScope; 
use App\Services\BranchManager;

class CashIn extends Model
{
    use HasFactory;
    public function finance_category()
    {
        return $this->belongsTo(FinanceCategory::class);
    }
    public function finance_sub_category()
    {
        return $this->belongsTo(FinanceSubCategory::class);
    }

    public function transaction(): MorphOne
    {
        return $this->morphOne(Transaction::class, 'reference', 'reference_table', 'reference_id');
    }

    public function balance(): BelongsTo
    {
        return $this->belongsTo(Balance::class, 'type', 'payment_way');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    protected static function booted(): void
    {
        // Apply the global scope
        static::addGlobalScope(new BranchScope);

        // Automatically set branch_id when creating new records for this model
        static::creating(function ($model) {
            // Get BranchManager instance
            $branchManager = app(BranchManager::class); 

            // Only set if branch context exists AND branch_id isn't already being set manually
            if ($branchManager->isBranchContextSet() && is_null($model->branch_id)) {
                $model->branch_id = $branchManager->getBranchId(); 
            }
        });
    }
}
