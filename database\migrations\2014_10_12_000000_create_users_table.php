<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('password');
            $table->string('phone')->unique();
            $table->string('profile_img')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->text('city')->nullable();
            $table->text('address')->nullable();
            $table->enum('gender',['male','female'])->nullable();
            $table->date('date_of_join')->nullable();
            $table->string('school')->nullable();
            $table->string('mother_phone')->nullable();
            $table->string('father_phone')->nullable();
            $table->string('mother_job')->nullable();
            $table->string('father_job')->nullable();
            $table->boolean('status')->default(0)->nullable();
            $table->boolean('hezb')->default(0)->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
