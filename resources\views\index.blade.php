@extends('layouts.dashboard')
@section('page.title', 'Dashboard')
@php
    use Carbon\Carbon;
@endphp
@section('content')
    @include('partials.inputError')
    {{-- //SECTION - Charts Reports --}}
    <div class="row ">
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title text-capitalize fw-bolder text-center">All Students</h6>
                    <canvas id="studentChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title text-capitalize fw-bolder text-center">Classrooms</h6>
                    <canvas id="classroomsChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title text-capitalize fw-bolder">Courses Status</h5>
                    <div class="d-flex justify-content-around mt-4">
                        <div class="text-center">
                            <div class="rounded-circle bg-success text-white d-flex justify-content-center align-items-center mx-auto"
                                style="width: 60px; height: 60px;">
                                <h4 class="m-0">{{ $publishedCourses }}</h4>
                            </div>
                            <small class="d-block mt-2 text-success fw-bold">Published</small>
                        </div>
                        <div class="text-center">
                            <div class="rounded-circle bg-secondary text-white d-flex justify-content-center align-items-center mx-auto"
                                style="width: 60px; height: 60px;">
                                <h4 class="m-0">{{ $notPublishedCourses }}</h4>
                            </div>
                            <small class="d-block mt-2 text-muted fw-bold">Not Published</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder text-center">Instructors</h5>
                    <div class="mt-4 rounded-circle bg-success text-white d-flex justify-content-center align-items-center mx-auto"
                        style="width: 60px; height: 60px;">
                        <h3 class="m-0">{{ $currentInstructors }}</h3>
                    </div>
                    <small class="text-center d-block mt-2 text-success fw-bold">Current Instructors</small>
                </div>
            </div>
        </div>
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title text-capitalize fw-bolder">Enrollments/Req Status</h5>
                    <div class="d-flex justify-content-around mt-4">
                        <div class="text-center">
                            <div class="rounded-circle bg-secondary text-white d-flex justify-content-center align-items-center mx-auto"
                                style="width: 60px; height: 60px;">
                                <h4 class="m-0">{{ $pendingEnrollments }}</h4>
                            </div>
                            <small class="d-block mt-2 text-muted fw-bold">Pending</small>
                        </div>
                        <div class="text-center">
                            <div class="rounded-circle bg-success text-white d-flex justify-content-center align-items-center mx-auto"
                                style="width: 60px; height: 60px;">
                                <h4 class="m-0">{{ $acceptedEnrollments }}</h4>
                            </div>
                            <small class="d-block mt-2 text-success fw-bold">Accepted</small>
                        </div>
                        <div class="text-center">
                            <div class="rounded-circle bg-danger text-white d-flex justify-content-center align-items-center mx-auto"
                                style="width: 60px; height: 60px;">
                                <h4 class="m-0">{{ $rejectedEnrollments }}</h4>
                            </div>
                            <small class="d-block mt-2 text-danger fw-bold">Rejected</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title text-capitalize fw-bolder">Contacts Status</h5>
                    <div class="d-flex justify-content-around mt-4">
                        <div class="text-center">
                            <div class="rounded-circle bg-secondary text-white d-flex justify-content-center align-items-center mx-auto"
                                style="width: 60px; height: 60px;">
                                <h4 class="m-0">{{ $pendingContacts }}</h4>
                            </div>
                            <small class="d-block mt-2 text-muted fw-bold">Pending</small>
                        </div>
                        <div class="text-center">
                            <div class="rounded-circle bg-success text-white d-flex justify-content-center align-items-center mx-auto"
                                style="width: 60px; height: 60px;">
                                <h4 class="m-0">{{ $respondedContacts }}</h4>
                            </div>
                            <small class="d-block mt-2 text-success fw-bold">Responded</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder">Enrollments/month</h5>
                    <div class="rounded-circle bg-success text-white d-flex justify-content-center align-items-center mx-auto mt-4"
                        style="width: 55px; height: 55px;">
                        <h3 class="m-0">{{ $enrollmentsThisMonth }}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder">New Groups/month</h5>
                    <div class="rounded-circle bg-success text-white d-flex justify-content-center align-items-center mx-auto"
                        style="width: 55px; height: 55px;">
                        <h3 class="m-0">{{ $newGroups }}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title text-capitalize fw-bolder">Refund/Month</h6>
                    {{-- <h1 class="text-center text-primary">{{$enrollmentRequests}}</h1> --}}
                </div>
            </div>
        </div>
    </div>

    <hr>
    <div class="d-flex justify-content-end mb-3">
        <form action="{{ route('export.analysis') }}" method="POST">
            @csrf
            <input type="hidden" name="newStudentsData" value="{{ json_encode($newStudentsData) }}">
            <input type="hidden" name="newEnrollmentsData" value="{{ json_encode($newEnrollmentsData) }}">
            <input type="hidden" name="newGroupsData" value="{{ json_encode($newGroupsData) }}">
            <input type="hidden" name="studentsData" value="{{ json_encode($studentsData) }}">
            <input type="hidden" name="enrollmentsData" value="{{ json_encode($enrollmentsData) }}">
            <input type="hidden" name="groupsData" value="{{ json_encode($groupsData) }}">
            <button type="submit" class="btn btn-success mb-3">Export Analysis to Excel</button>
        </form>
    </div>

    <div class="row">
        <div class="col-xl-6 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder text-primary">(New Students Count)/Month - Analysis
                    </h5>
                    <canvas id="newStudentsChart" style="height: 400px; width: 100%;"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder text-primary">(Total Students Count)/Month - Analysis
                    </h5>
                    <canvas id="studentsChart" style="height: 400px; width: 100%;"></canvas>
                </div>
            </div>
        </div>
    </div>
    <hr>
    <div class="row">
        <div class="col-xl-6 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder text-primary">(New Groups Count)/Month - Analysis</h5>
                    <canvas id="newGroupsChart" style="height: 400px; width: 100%;"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder text-primary">(Total Groups Count)/Month - Analysis
                    </h5>
                    <canvas id="groupsChart" style="height: 400px; width: 100%;"></canvas>
                </div>
            </div>
        </div>
    </div>
    <hr>
    <div class="row">
        <div class="col-xl-6 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder text-primary">(New Enrollments Count)/Month - Analysis
                    </h5>
                    <canvas id="newEnrollmentsChart" style="height: 400px; width: 100%;"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title text-capitalize fw-bolder text-primary">(Total Enrollments Count)/Month -
                        Analysis
                    </h5>
                    <canvas id="enrollmentsChart" style="height: 400px; width: 100%;"></canvas>
                </div>
            </div>
        </div>
    </div>
    <hr>
    {{-- //SECTION - Tables and Filtrations --}}
    <div class="row mt-4">
        <div class="col-xl-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5 class=" text-primary text-capitalize text-center fw-bolder">Instructors - Comments of Sessions</h5>
                    <div class="d-flex justify-content-end mb-3">
                        <a href="{{ route('export.comments', ['month' => request('month')]) }}"
                            class="btn btn-success">Export
                            Comments</a>
                    </div>
                    <div class="col-xl-4 mt-3">
                        <form method="GET" action="{{ url()->current() }}" id="comments-filter-form">
                            <div class="form-group d-flex align-items-center">
                                <label for="comments-filter" class="text-primary mr-2 fw-bolder"
                                    style="white-space: nowrap;">Filter By:</label>
                                <input type="month" id="month" name="month"
                                    class="form-control form-control-sm mx-2 text-center fw-bolder border-secondary"
                                    value="{{ request('month') }}">
                            </div>
                        </form>
                    </div>
                    <hr>
                    <div class="table-responsive">
                        <table class="table text-center">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Course</th>
                                    <th>Session</th>
                                    <th>Session Date</th>
                                    <th>Instructor</th>
                                    <th>Comment</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if ($sessions->isEmpty())
                                    <tr>
                                        <td class="text-danger" colspan="6">No data available</td>
                                    </tr>
                                @else
                                    @foreach ($sessions as $index => $session)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>{{ $session->courseCycle->level->course->name ?? 'N/A' }}</td>
                                            <td>{{ $session->session->name }}</td>
                                            <td>{{ Carbon::parse($session->date)->format('Y-m-d') }}</td>
                                            <td>{{ $session->courseCycle->instructor->name ?? 'N/A' }}</td>
                                            <td class="text-wrap">{{ $session->comment }}</td>
                                        </tr>
                                    @endforeach
                                @endif
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-2">
                        {{ $sessions->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr>
    <div class="row mt-4">
        <div class="col-xl-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5 class=" text-primary text-capitalize text-center fw-bolder">Students Grades</h5>
                    <div class="d-flex justify-content-end mb-3">
                        <a href="{{ route('export.grades', ['month' => request('month'), 'student_name' => request('student_name'), 'course_cycle_name' => request('course_cycle_name')]) }}"
                            class="btn btn-success">Export Grades</a>
                    </div>
                    <div class="col-xl-12 mt-3">
                        <form method="GET" action="{{ url()->current() }}" id="grades-filter-form">
                            <div class="form-group d-flex align-items-center">
                                <label for="grades-filter" class="text-primary mr-2 fw-bolder"
                                    style="white-space: nowrap;">Filter By:</label>
                                <input type="month" id="month" name="month"
                                    class="form-control form-control-sm mx-2 text-center fw-bolder border-secondary"
                                    value="{{ request('month') }}">
                                <input type="text" name="student_name" value="{{ request('student_name') }}"
                                    class="form-control form-control-sm mx-2" placeholder="Student Name">
                                <input type="text" name="course_cycle_name"
                                    value="{{ request('course_cycle_name') }}" class="form-control form-control-sm mx-2"
                                    placeholder="Course Cycle Name">
                                <button type="submit" class="btn btn-primary btn-sm mx-2">Filter</button>
                            </div>
                        </form>
                    </div>
                    <hr>
                    <table class="table text-center">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Student</th>
                                <th>Grade</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if ($gradesQuery->isEmpty())
                                <tr>
                                    <td class="text-danger" colspan="6">No data available</td>
                                </tr>
                            @else
                                @foreach ($gradesQuery as $index => $grade)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $grade->user->name }}</td>
                                        <td>{{ $grade->grade }}</td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>

                    <div class="mt-2">
                        {{ $gradesQuery->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <hr>
    @if (auth('admin')->user()->role == 'super_admin' || auth('admin')->user()->role == 'admin')
        <div class="row mt-4">
            <div class="col-xl-12">
                <div class="card bg-light">
                    <div class="card-body">
                        <h5 class=" text-primary text-capitalize text-center fw-bolder">Instructors Salaries</h5>
                        <div class="d-flex justify-content-end mb-3">
                            <a href="{{ route('export.salaries', ['month' => request('month'), 'instructor_name' => request('instructor_name')]) }}"
                                class="btn btn-success">Export Salaries</a>
                        </div>
                        <div class="col-xl-12 mt-3">
                            <form method="GET" action="{{ url()->current() }}" id="salaries-filter-form">
                                <div class="form-group d-flex align-items-center">
                                    <label for="salaries-filter" class="text-primary mr-2 fw-bolder"
                                        style="white-space: nowrap;">Filter By:</label>
                                    <input type="month" id="month" name="month"
                                        class="form-control form-control-sm mx-2 text-center fw-bolder border-secondary"
                                        value="{{ request('month') }}">
                                    <input type="text" name="instructor_name"
                                        value="{{ request('instructor_name') }}"
                                        class="form-control form-control-sm mx-2" placeholder="Instructor Name">
                                    <button type="submit" class="btn btn-primary btn-sm mx-2">Filter</button>
                                </div>
                            </form>
                        </div>
                        <hr>
                        <table class="table text-center">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Instructor</th>
                                    <th>Working Hours</th>
                                    @if (auth('admin')->user()->role == 'super_admin')
                                        <th>Salary</th>
                                    @endif
                                </tr>
                            </thead>
                            <tbody>
                                @if ($salariesQuery->isEmpty())
                                    <tr>
                                        <td class="text-danger" colspan="6">No data available</td>
                                    </tr>
                                @else
                                    @foreach ($salariesQuery as $index => $salary)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>{{ $salary->instructor->name }}</td>
                                            <td>{{ $salary->work_hours }}</td>
                                            @if (auth('admin')->user()->role == 'super_admin')
                                                <td>{{ $salary->salary }}</td>
                                            @endif
                                        </tr>
                                    @endforeach
                                @endif
                            </tbody>
                        </table>
                        <div class="mt-2">
                            {{ $salariesQuery->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
    {{-- //!SECTION --}}
@endsection

@section('chart')
    <script>
        document.getElementById('month').addEventListener('change', function() {
            document.getElementById('comments-filter-form').submit();
        });
        Chart.defaults.set('plugins.datalabels', {
            color: '#fff',
            font: {
                size: 14,
            }
        });
        const stdChart = document.getElementById('studentChart');
        const classroomsChart = document.getElementById('classroomsChart');

        const studentsData = {
            labels: [
                'Active',
                'Not Active',
            ],
            datasets: [{
                label: 'Students',
                data: [
                    {{ \DB::table('users')->where('status', 1)->where('branch_id', app(\App\Services\BranchManager::class)->getBranchId())->count() }},
                    {{ \DB::table('users')->where('status', 0)->where('branch_id', app(\App\Services\BranchManager::class)->getBranchId())->count() }},

                ],
                backgroundColor: [
                    '#3498db',
                    '#808e9b',
                ],
                hoverOffset: 4
            }]
        };

        const classroomsData = {
            labels: [
                'Not Started',
                'In Progress',
                'Completed',
            ],
            datasets: [{
                label: 'Courses',
                data: [
                    {{ \DB::table('course_cycles')->where('course_status', 'not_started')->where('branch_id', app(\App\Services\BranchManager::class)->getBranchId())->count() }},
                    {{ \DB::table('course_cycles')->where('course_status', 'in_progress')->where('branch_id', app(\App\Services\BranchManager::class)->getBranchId())->count() }},
                    {{ \DB::table('course_cycles')->where('course_status', 'completed')->where('branch_id', app(\App\Services\BranchManager::class)->getBranchId())->count() }},
                ],
                backgroundColor: [
                    '#808e9b',
                    '#3498db',
                    '#62A03F',
                ],
                hoverOffset: 4
            }]
        };


        new Chart(stdChart, {
            plugins: [ChartDataLabels],
            type: 'doughnut',
            data: studentsData,
            options: {
                aspectRatio: 2,
            }
        })
        new Chart(classroomsChart, {
            plugins: [ChartDataLabels],
            type: 'doughnut',
            data: classroomsData,
            options: {
                aspectRatio: 2,
            }
        })
        document.addEventListener('DOMContentLoaded', function() {
            // Charts Elements Variables
            var newStudentsChart = document.getElementById('newStudentsChart').getContext('2d');
            var newEnrollmentsChart = document.getElementById('newEnrollmentsChart').getContext('2d');
            var newGroupsChart = document.getElementById('newGroupsChart').getContext('2d');
            var studentsChart = document.getElementById('studentsChart').getContext('2d');
            var enrollmentsChart = document.getElementById('enrollmentsChart').getContext('2d');
            var groupsChart = document.getElementById('groupsChart').getContext('2d');

            // Montly-Data Variables
            var years = @json($years);
            var newStudentsData = @json($newStudentsData);
            var newEnrollmentsData = @json($newEnrollmentsData);
            var newGroupsData = @json($newGroupsData);
            var studentsData = @json($studentsData);
            var enrollmentsData = @json($enrollmentsData);
            var groupsData = @json($groupsData);

            // DataSet-Analysis Variables
            var newStudentsDatasets = years.map(year => ({
                label: `${year}`,
                data: Object.values(newStudentsData[year]),
                borderColor: getRandomColor(),
                backgroundColor: getRandomColor(0.2),
                fill: false,
            }));
            var newEnrollmentsDatasets = years.map(year => ({
                label: `${year}`,
                data: Object.values(newEnrollmentsData[year]),
                borderColor: getRandomColor(),
                backgroundColor: getRandomColor(0.2),
                fill: false,
            }));
            var newGroupsDatasets = years.map(year => ({
                label: `${year}`,
                data: Object.values(newGroupsData[year]),
                borderColor: getRandomColor(),
                backgroundColor: getRandomColor(0.2),
                fill: false,
            }));
            var studentsDatasets = years.map(year => ({
                label: `${year}`,
                data: Object.values(studentsData[year]),
                borderColor: getRandomColor(),
                backgroundColor: getRandomColor(0.2),
                fill: false,
            }));
            var enrollmentsDatasets = years.map(year => ({
                label: `${year}`,
                data: Object.values(enrollmentsData[year]),
                borderColor: getRandomColor(),
                backgroundColor: getRandomColor(0.2),
                fill: false,
            }));
            var groupsDatasets = years.map(year => ({
                label: `${year}`,
                data: Object.values(groupsData[year]),
                borderColor: getRandomColor(),
                backgroundColor: getRandomColor(0.2),
                fill: false,
            }));
            // Creating Charts using DataSets
            var newStudentschart = new Chart(newStudentsChart, {
                type: 'line',
                data: {
                    labels: [...Array(12).keys()].map(i => new Date(0, i).toLocaleString('default', {
                        month: 'short'
                    })),
                    datasets: newStudentsDatasets
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                text: 'Month'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                text: 'Students'
                            }
                        }
                    }
                }
            });
            var newEnrollmetschart = new Chart(newEnrollmentsChart, {
                type: 'line',
                data: {
                    labels: [...Array(12).keys()].map(i => new Date(0, i).toLocaleString('default', {
                        month: 'short'
                    })),
                    datasets: newEnrollmentsDatasets
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                text: 'Month'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                text: 'Enrollments'
                            }
                        }
                    }
                }
            });
            var newGroupschart = new Chart(newGroupsChart, {
                type: 'line',
                data: {
                    labels: [...Array(12).keys()].map(i => new Date(0, i).toLocaleString('default', {
                        month: 'short'
                    })),
                    datasets: newGroupsDatasets
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                text: 'Month'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                text: 'Groups'
                            }
                        }
                    }
                }
            });
            var studentschart = new Chart(studentsChart, {
                type: 'line',
                data: {
                    labels: [...Array(12).keys()].map(i => new Date(0, i).toLocaleString('default', {
                        month: 'short'
                    })),
                    datasets: studentsDatasets
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                text: 'Month'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                text: 'Students'
                            }
                        }
                    }
                }
            });
            var enrollmetschart = new Chart(enrollmentsChart, {
                type: 'line',
                data: {
                    labels: [...Array(12).keys()].map(i => new Date(0, i).toLocaleString('default', {
                        month: 'short'
                    })),
                    datasets: enrollmentsDatasets
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                text: 'Month'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                text: 'Enrollments'
                            }
                        }
                    }
                }
            });
            var groupschart = new Chart(groupsChart, {
                type: 'line',
                data: {
                    labels: [...Array(12).keys()].map(i => new Date(0, i).toLocaleString('default', {
                        month: 'short'
                    })),
                    datasets: groupsDatasets
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                text: 'Month'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                text: 'Groups'
                            }
                        }
                    }
                }
            });
            function getRandomColor(alpha = 1) {
                return `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${alpha})`;
            }
        });
    </script>
@endsection
