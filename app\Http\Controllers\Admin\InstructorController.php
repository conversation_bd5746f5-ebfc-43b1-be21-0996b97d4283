<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\InstructorRequest;
use App\Http\Requests\Admin\UpdateInstructorRequest;
use App\Models\Admin;
use App\Models\AdminSalary;
use App\Models\CourseCycle;
use App\Models\Instructor;
use App\Models\InstructorAttendance;
use App\Models\InstructorSalary;
use App\Models\StaffSalary;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;


class InstructorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $instructors = Instructor::orderBy('created_at', 'desc');

        if ($search) {
            $instructors = $instructors
                ->whereAny(['name', 'phone', 'email'], 'like', '%' . request()->get('search', '') . '%');
        }

        confirmDelete('Delete Instructor!', "Are you sure you want to delete this instructor?");

        return view('instructors.index', [
            'instructors' => $instructors->paginate(12)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('instructors.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(InstructorRequest $request)
    {
        Instructor::create($request->except('_token', 'password') +
            [
                'password' => Hash::make($request->get('password'))
            ]);

        return redirect()->route('instructors.index')->with('add', 'Instructor Added Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Instructor $instructor)
    {
        // Get current month and year
        $currentMonth = now()->month;
        $currentYear = now()->year;

        // Calculate start and end date of current month
        $startDate = now()->startOfMonth();
        $endDate = now()->endOfMonth();

        // Fetch instructor's attendance records for sessions within the current month
        $attendances = $instructor->instructor_attendances()
            ->join('session_cycles', 'instructor_attendances.session_cycle_id', '=', 'session_cycles.id')
            ->whereBetween('session_cycles.date', [$startDate, $endDate])
            ->get();

        // Count the number of sessions attended by the instructor during the current month
        $numberOfSessions = $instructor->instructor_attendances()
            ->join('session_cycles', 'instructor_attendances.session_cycle_id', '=', 'session_cycles.id')
            ->whereBetween('session_cycles.date', [$startDate, $endDate])
            ->count();

        // Calculate total work hours by multiplying the number of sessions by 2
        $totalWorkHours = $numberOfSessions * 2;

        // Calculate total salary for sessions in current month
        $totalSalary = $attendances->sum('salary/hour');

        // Multiply total salary by 2
        $totalSalary *= 2;

        $courses = CourseCycle::where('instructor_id', $instructor->id)->paginate(8);
        return view('instructors.show', compact('instructor', 'courses', 'totalSalary', 'totalWorkHours'));
    }


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Instructor $instructor)
    {
        return view('instructors.edit', compact('instructor'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateInstructorRequest $request, Instructor $instructor)
    {
        $data = $request->except('_token', '_method');
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->get('password'));
        } else {
            $data['password'] = $instructor->password;
        }

        $instructor->update($data);

        return redirect()->route('instructors.index')->with('update', 'Data of ' . $instructor->name . ' Updated Successfully');
    }

    public function updateProfilePicture(Request $request, Instructor $instructor)
    {
        // Validate the incoming request
        $request->validate([
            'profile_img' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);
        // Check if a profile image is uploaded
        if ($request->hasFile('profile_img')) {
            $imageName = time() . '.' . $request->profile_img->extension();
            $request->profile_img->move(('images/instructors'), $imageName);
        }
        $instructor->update(['profile_img' => $imageName]);
        return redirect()->route('instructors.show', $instructor)->with('add', 'Profile Picture Added Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Instructor $instructor)
    {
        $instructor->delete();
        return redirect()->route('instructors.index')->with('delete', 'Instructor Deleted Successfully');
    }

    public function salary(Request $request)
    {
        $nameFilter = $request->query('name', '');
        $monthFilter = $request->query('month', '');
    
        if ($monthFilter) {
            $selectedMonth = Carbon::createFromFormat('Y-m', $monthFilter)->format('Y-m');
        } else {
            $selectedMonth = Carbon::now()->format('Y-m');
        }
    
        $instructorSalariesQuery = InstructorSalary::forCurrentBranch()->whereBetween('created_at', [
            Carbon::createFromFormat('Y-m', $selectedMonth)->startOfMonth(),
            Carbon::createFromFormat('Y-m', $selectedMonth)->endOfMonth()
        ])
            ->when($nameFilter, function ($query) use ($nameFilter) {
                return $query->whereHas('instructor', function ($query) use ($nameFilter) {
                    $query->where('name', 'like', "%$nameFilter%");
                });
            })
            ->with(['instructor'])
            ->paginate(5);
    
        $staffSalariesQuery = StaffSalary::forCurrentBranch()->where('month', 'like', "$selectedMonth%")
            ->when($nameFilter, function ($query) use ($nameFilter) {
                return $query->whereHas('staff', function ($query) use ($nameFilter) {
                    $query->where('name', 'like', "%$nameFilter%");
                });
            })
            ->with(['staff'])
            ->paginate(5);
    
        $adminSalariesQuery = AdminSalary::forCurrentBranch()->where('month', 'like', "$selectedMonth%")
            ->when($nameFilter, function ($query) use ($nameFilter) {
                return $query->whereHas('admin', function ($query) use ($nameFilter) {
                    $query->where('name', 'like', "%$nameFilter%");
                });
            })
            ->with(['admin'])
            ->paginate(5);
    
        return view('instructors.salary', compact('instructorSalariesQuery', 'staffSalariesQuery', 'adminSalariesQuery', 'selectedMonth', 'nameFilter'));
    }


}
