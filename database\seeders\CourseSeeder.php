<?php

namespace Database\Seeders;

use App\Models\Course;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Course::create([
            'name' => '2D Gaming by Scratch',
            'comm_name' => 'Classic Game Design Club',
            'start_age' => 7,
            'end_age' => 12,
            'hours' => 48,
            'levels' => 3,
            'sessions' => 24,
            'months' => 6,
            'price' => 450.00,
            'exp_level' => 'beginner',
            'short_desc' => 'Enables kids to design and program their own 2D games using Scratch.',
            'long_desc' => 'Scratch is a visual programming language. Students will create 2D games by dragging and connecting blocks. Learn about sprites, animations, and game logic.',
            'image_path' => '1.webp',
            'track_id' => 2,
        ]);
        Course::create([
            'name' => '3D Gaming by Unity',
            'comm_name' => 'Advanced 3D Game Design Club',
            'start_age' => 12,
            'end_age' => 18,
            'hours' => 48,
            'levels' => 3,
            'sessions' => 24,
            'months' => 6,
            'price' => 600.00,
            'exp_level' => 'intermediate',
            'short_desc' => 'Covers game design and development using the Unity engine.',
            'long_desc' => 'Dive into creating 3D games using Unity. Learn about game mechanics, physics, lighting, and optimization. Explore multiplayer features and create immersive gaming experiences.',
            'image_path' => '2.webp',
            'track_id' => 2,
        ]);
        Course::create([
            'name' => '3D Modelling',
            'comm_name' => '3D Sculpting Studio',
            'start_age' => 10,
            'end_age' => 18,
            'hours' => 48,
            'levels' => 3,
            'sessions' => 24,
            'months' => 6,
            'price' => 600.00,
            'exp_level' => 'beginner',
            'short_desc' => 'Teaches the basics of creating 3D models for various applications.',
            'long_desc' => 'Learn to create 3D models using software like Blender, Tinkercad, or Fusion 360. Understand concepts like mesh modeling, texturing, and rendering.',
            'image_path' => '3.webp',
            'track_id' => 2,
        ]);
        Course::create([
            'name' => 'AI using mBlock',
            'comm_name' => 'Artificial Intelligence Playground',
            'start_age' => 8,
            'end_age' => 12,
            'hours' => 32,
            'levels' => 2,
            'sessions' => 16,
            'months' => 4,
            'price' => 500.00,
            'exp_level' => 'intermediate',
            'short_desc' => 'Introduces artificial intelligence concepts using mBlock.',
            'long_desc' => 'Explore artificial intelligence (AI) concepts using mBlock. Learn about machine learning, neural networks, and natural language processing. Apply AI techniques to robotics projects.',
            'image_path' => '4.webp',
            'track_id' => 3,
        ]);
        Course::create([
            'name' => 'App Inventor',
            'comm_name' => 'App Creation Club',
            'start_age' => 10,
            'end_age' => 14,
            'hours' => 48,
            'levels' => 3,
            'sessions' => 24,
            'months' => 6,
            'price' => 500.00,
            'exp_level' => 'intermediate',
            'short_desc' => 'Guides students in developing their own apps using MIT\'s App Inventor.',
            'long_desc' => 'App Inventor allows students to create Android apps using a visual interface. Learn about UI design, event handling, and app functionality. Build practical apps like calculators, quizzes, or location-based tools.',
            'image_path' => '5.webp',
            'track_id' => 3,
        ]);
        Course::create([
            'name' => 'Arduino',
            'comm_name' => 'Gadget Making Club',
            'start_age' => 11,
            'end_age' => 18,
            'hours' => 64,
            'levels' => 4,
            'sessions' => 32,
            'months' => 8,
            'price' => 500.00,
            'exp_level' => 'intermediate',
            'short_desc' => 'Hands-on introduction to electronics and programming with Arduino boards.',
            'long_desc' => 'Arduino courses cover microcontroller programming using the Arduino platform. Students will build projects such as sensors, robots, and interactive devices.',
            'image_path' => '6.webp',
            'track_id' => 1,
        ]);
        Course::create([
            'name' => 'Programming by Scratch',
            'comm_name' => 'Creative Coding Club',
            'start_age' => 7,
            'end_age' => 12,
            'hours' => 48,
            'levels' => 3,
            'sessions' => 24,
            'months' => 6,
            'price' => 450.00,
            'exp_level' => 'beginner',
            'short_desc' => 'Expands on the basics of coding to create interactive stories and games.',
            'long_desc' => 'Go beyond game development in Scratch. Explore algorithms, data structures, and interactive storytelling.',
            'image_path' => '7.webp',
            'track_id' => 3,
        ]);
        Course::create([
            'name' => 'Python Programming for beginners',
            'comm_name' => 'Junior PyPath Club',
            'start_age' => 12,
            'end_age' => 18,
            'hours' => 48,
            'levels' => 3,
            'sessions' => 24,
            'months' => 6,
            'price' => 500.00,
            'exp_level' => 'intermediate',
            'short_desc' => 'Introduces Python programming in a kid-friendly way.',
            'long_desc' => 'Introduce Python to young learners. Cover syntax, data types, loops, and functions. Create simple projects like calculators or text-based games.',
            'image_path' => '8.webp',
            'track_id' => 3,
        ]);
        Course::create([
            'name' => 'UI using Figma',
            'comm_name' => 'Figma Frontiers',
            'start_age' => 10,
            'end_age' => 12,
            'hours' => 16,
            'levels' => 1,
            'sessions' => 8,
            'months' => 2,
            'price' => 600.00,
            'exp_level' => 'beginner',
            'short_desc' => 'beginner-friendly course designed to teach the fundamentals of UI design using Figma, the leading interface design tool.',
            'long_desc' => 'Figma Frontiers offers a comprehensive introduction to the world of user interface design through the lens of Figma, one of the most popular design tools used by professionals worldwide. This course is tailored for beginners and aims to equip students with the skills necessary to design beautiful, functional interfaces from scratch. Students will learn how to navigate Figma’s user interface, utilize its powerful features for creating wireframes, prototypes, and high-fidelity designs, and understand the principles of UI design including color theory, typography, and layout techniques. By the end of the course, participants will have the confidence to tackle their own design projects and collaborate effectively within design teams. Whether you\'re looking to kickstart a career in UI design or just want to enhance your digital skills, Figma Frontiers is your gateway to mastering the art of making user-centric design decisions with precision and creativity.',
            'image_path' => '9.webp',
            'track_id' => 4,
        ]);
        Course::create([
            'name' => 'Python Prompt Engineering',
            'comm_name' => 'PyPrompters Club',
            'start_age' => 12,
            'end_age' => 18,
            'hours' => 32,
            'levels' => 2,
            'sessions' => 16,
            'months' => 4,
            'price' => 500.00,
            'exp_level' => 'intermediate',
            'short_desc' => 'PyPrompters Club is an engaging course designed to introduce beginners to the art of prompt engineering with Python.',
            'long_desc' => 'PyPrompters Club offers a detailed and interactive learning experience aimed at beginners who want to delve into the innovative field of prompt engineering using Python. This course explores the fundamental concepts of prompt engineering, including designing prompts that effectively communicate tasks to AI and machine learning systems. Participants will learn Python programming basics and how to apply these skills to develop prompts that optimize the performance of language models. The curriculum covers a variety of techniques, from simple command-based prompts to more complex prompts that involve conditional logic and creativity. By the end of the course, students will not only grasp the technicalities of Python but also understand how to strategically use language to interact with advanced AI systems. PyPrompters Club is perfect for those curious about the intersection of language and technology and eager to pioneer new ways of interacting with AI.',
            'image_path' => '10.webp',
            'track_id' => 3,
        ]);
        Course::create([
            'name' => 'Web Development',
            'comm_name' => 'Web Crafting Club',
            'start_age' => 12,
            'end_age' => 18,
            'hours' => 48,
            'levels' => 3,
            'sessions' => 24,
            'months' => 6,
            'price' => 600.00,
            'exp_level' => 'intermediate',
            'short_desc' => 'Get started in web frontend development! Learn the essentials of HTML, CSS, and JavaScript to build modern, interactive websites.',
            'long_desc' => 'Our web frontend development course covers the fundamentals of HTML, CSS, and JavaScript, equipping you with the skills to create engaging and interactive websites. Perfect for beginners, this course will teach you how to structure web content, style it effectively, and add dynamic behavior to your pages. By the end, you\'ll be ready to build your own professional-looking websites from scratch.',
            'image_path' => '11.webp',
            'track_id' => 4,
        ]);
        Course::create([
            'name' => 'Scratch Junior',
            'comm_name' => 'Coding Crafting Junior',
            'start_age' => 5,
            'end_age' => 7,
            'hours' => 32,
            'levels' => 2,
            'sessions' => 16,
            'months' => 4,
            'price' => 450.00,
            'exp_level' => 'beginner',
            'short_desc' => 'Introduces young children to basic programming concepts through visual blocks.',
            'long_desc' => 'Scratch Junior is designed for young children (ages 5-7). They can create interactive stories and games, learning fundamental programming concepts in a fun and creative way.',
            'image_path' => '12.webp',
            'track_id' => 2,
        ]);
        Course::create([
            'name' => 'Spike Essential (Icon blocks)',
            'comm_name' => 'MiniBots Club',
            'start_age' => 5,
            'end_age' => 7,
            'hours' => 32,
            'levels' => 2,
            'sessions' => 16,
            'months' => 4,
            'price' => 500.00,
            'exp_level' => 'beginner',
            'short_desc' => 'Engages students in playful learning to build confidence in the STEAM area.',
            'long_desc' => 'Spike Essential introduces young learners (ages 5-7) to robotics and coding using the LEGO® Education SPIKE™ Prime platform. Students will explore basic programming concepts and build simple robots.',
            'image_path' => '13.webp',
            'track_id' => 1,
        ]);
        Course::create([
            'name' => 'Spike Essential (Word blocks)',
            'comm_name' => 'Robo Builders Club',
            'start_age' => 7,
            'end_age' => 10,
            'hours' => 32,
            'levels' => 2,
            'sessions' => 16,
            'months' => 4,
            'price' => 500.00,
            'exp_level' => 'beginner',
            'short_desc' => 'Engages students in playful learning to build confidence in the STEAM area.',
            'long_desc' => 'Spike Essential introduces young learners (ages 7-10) to robotics and coding using the LEGO® Education SPIKE™ Prime platform. Students will explore basic programming concepts and build simple robots.',
            'image_path' => '14.webp',
            'track_id' => 1,
        ]);
        Course::create([
            'name' => 'Spike Prime',
            'comm_name' => 'Next Level Robo Builders Club',
            'start_age' => 10,
            'end_age' => 14,
            'hours' => 48,
            'levels' => 3,
            'sessions' => 24,
            'months' => 6,
            'price' => 500.00,
            'exp_level' => 'intermediate',
            'short_desc' => 'Develops critical thinking and problem-solving skills through robotics.',
            'long_desc' => 'Spike Prime dives deeper into robotics and coding for ages 10-14. Students will create more complex robots, learn about sensors, and explore real-world applications.',
            'image_path' => '15.webp',
            'track_id' => 1,
        ]);

    }
}
