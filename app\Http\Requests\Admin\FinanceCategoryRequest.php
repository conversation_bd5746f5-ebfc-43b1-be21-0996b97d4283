<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\UniqueCaseInsensitive;


class FinanceCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $financeCategoryId = $this->route('finance_category') ? $this->route('finance_category')->id : null;

        return [
            'name' => ['required', 'min:3', new UniqueCaseInsensitive('finance_categories', 'name', $financeCategoryId)],
            'type' => 'required|in:cash_in,cash_out,both',
        ];
    }
}
