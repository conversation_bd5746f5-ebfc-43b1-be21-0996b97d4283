<?php

namespace App\Http\Requests\API;

use App\Http\Requests\API\FormRequest;

class SendStudentComment extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|array|min:1',
            'user_id.*' => 'integer|exists:student_attendances,user_id,session_cycle_id,' . $this->route('session_cycle_id'),
            'comment' => 'required|string',
        ];
    }
}
