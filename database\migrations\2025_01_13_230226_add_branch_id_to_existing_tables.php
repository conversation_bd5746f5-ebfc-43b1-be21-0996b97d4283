<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        $tables = [
            'users',
            'instructors',
            'events',
            'admins',
            'cash_ins',
            'cash_outs',
            'contacts',
            'course_cycles',
            'enroll_forms',
            'profits',
        ];

        foreach ($tables as $table) {
            if (!Schema::hasColumn($table, 'branch_id')) {
                Schema::table($table, function (Blueprint $table) {
                    $table->foreignId('branch_id')->default(1)->before('created_at')->constrained()->cascadeOnUpdate();
                });
            }
        }
    }

    public function down(): void
    {
        $tables = [
            'users',
            'instructors',
            'events',
            'admins',
            'cash_ins',
            'cash_outs',
            'contacts',
            'course_cycles',
            'enroll_forms',
            'profits',
        ];

        foreach ($tables as $table) {
            if (Schema::hasColumn($table, 'branch_id')) {
                Schema::table($table, function (Blueprint $table) {
                    $table->dropForeign(['branch_id']);
                    $table->dropColumn('branch_id');
                });
            }
        }
    }
};