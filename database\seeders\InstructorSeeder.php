<?php

namespace Database\Seeders;

use App\Models\Instructor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class InstructorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Instructor::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01054567890',
            'profile_img' => 'instructor.jpg',
            'date_of_birth' => '1990-03-15',
            'city' => 'Portsaid',
            'address' => '22 El Maadi Street, Al Maadi',
            'gender' => 'male',
            'fixed_salary' => 55,
            'variable_salary' => 45,
            'job_type' => 'part_time',
            'date_of_join' => '2024-05-01',
            'status' => 1,
        ]);
        Instructor::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01004567891',
            'profile_img' => 'instructor.jpg',
            'date_of_birth' => '1985-09-20',
            'city' => 'Portsaid',
            'address' => '10 Sphinx Street, Haram',
            'gender' => 'male',
            'fixed_salary' => 35,
            'variable_salary' => 25,
            'job_type' => 'full_time',
            'date_of_join' => '2020-02-15',
            'status' => 1,
        ]);
        Instructor::create([
            'name' => 'Ahmed Mahmoud Hassan',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234500892',
            'profile_img' => 'instructor.jpg',
            'date_of_birth' => '1990-06-10',
            'city' => 'Portsaid',
            'address' => '5 Cleopatra Street, Al Shatby',
            'gender' => 'male',
            'fixed_salary' => 6500,
            'job_type' => 'full_time',
            'date_of_join' => '2021-01-10',
            'status' => 1,
        ]);
        Instructor::create([
            'name' => 'Youssef Ali Ibrahim',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234500893',
            'profile_img' => 'instructor.jpg',
            'date_of_birth' => '1988-03-25',
            'city' => 'Portsaid',
            'address' => '15 Karnak Street, Karnak',
            'gender' => 'male',
            'fixed_salary' => 6800,
            'job_type' => 'full_time',
            'date_of_join' => '2019-11-20',
            'status' => 1,
        ]);
        Instructor::create([
            'name' => 'Mohamed Hassan Mahmoud',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567800',
            'profile_img' => 'instructor.jpg',
            'date_of_birth' => '1987-07-15',
            'city' => 'Portsaid',
            'address' => '20 Suez Canal Street, Suez',
            'gender' => 'male',
            'fixed_salary' => 7200,
            'job_type' => 'full_time',
            'date_of_join' => '2022-04-05',
            'status' => 1,
        ]);
        Instructor::create([
            'name' => 'Amr Hassan Ali',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01204567806',
            'profile_img' => 'instructor.jpg',
            'date_of_birth' => '1989-05-20',
            'city' => 'Portsaid',
            'address' => '25 Red Sea Street, Al Mamsha',
            'gender' => 'male',
            'fixed_salary' => 6900,
            'job_type' => 'part_time',
            'date_of_join' => '2020-09-10',
            'status' => 1,
        ]);
        Instructor::create([
            'name' => 'Safa Adel Abdelsalam',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01012076064',
            'profile_img' => 'instructor.jpg',
            'date_of_birth' => '1989-05-20',
            'city' => 'Portsaid',
            'address' => '25 Red Sea Street, Al Mamsha',
            'gender' => 'male',
            'fixed_salary' => 6900,
            'variable_salary' => 6900,
            'job_type' => 'part_time',
            'date_of_join' => '2020-09-10',
            'status' => 1,
        ]);
    }
}