<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Session extends Model
{
    use HasFactory;

    public function session_materials()
    {
        return $this->hasMany(SessionMaterial::class);
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }
    
    public function session_cycles()
    {
        return $this->hasMany(SessionCycle::class);
    }
}
