<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('instructors', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('password');
            $table->string('profile_img')->nullable();
            $table->string('phone')->unique();
            $table->date('date_of_birth')->nullable();
            $table->string('city')->nullable();
            $table->text('address')->nullable();
            $table->enum('gender',['male','female'])->nullable();
            $table->decimal('fixed_salary')->nullable()->default(0);
            $table->decimal('variable_salary')->nullable()->default(0);
            $table->enum('job_type',['full_time','part_time'])->nullable();
            $table->date('date_of_join')->nullable();
            $table->boolean('status')->default(0)->nullable();
            $table->rememberToken()->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('instructors');
    }
};
