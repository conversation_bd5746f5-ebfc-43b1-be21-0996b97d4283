<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Course extends Model
{
    use HasFactory;

    protected static function boot()
    {
        parent::boot();

        static::created(function ($course) {
            // Create level1 for the newly created course
            $level = $course->levels()->create(['name' => 'Level 1']);
            $level->update(['course_price' => $course->price]);
        });
    }

    protected $fillable = ['image_path'];

    public function track()
    {
        return $this->belongsTo(Track::class);
    }

    public function enroll_forms()
    {
        return $this->hasMany(EnrollForm::class);
    }

    public function levels()
    {
        return $this->hasMany(Level::class);
    }
}
