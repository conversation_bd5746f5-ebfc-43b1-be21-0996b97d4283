<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminRequest;
use App\Models\Admin;
use App\Models\Branch;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $admins = Admin::forCurrentBranch()->where('role', '!=', 'super_admin')
            ->orderBy('created_at', 'desc');

        if ($search) {
            $admins = $admins
                ->whereAny(['name', 'phone', 'email'], 'like', '%' . request()->get('search', '') . '%');
        }

        confirmDelete('Delete Staff Member!', "Are you sure you want to delete this Staff-Member?");

        return view('admins.index', [
            'admins' => $admins->paginate(12)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = Branch::all();
        return view('admins.create', compact('branches'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AdminRequest $request)
    {
        $admin =  Admin::create($request->except('_token', 'password', 'branch_id') +
            [
                'password' => Hash::make($request->get('password'))
            ]);

        if ($request->has('branch_id')) {
            $admin->branches()->attach($request->get('branch_id'));
        }
        return redirect()->route('admins.index')->with('add', 'Staff-Member Added Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Admin $admin)
    {
        return view('admins.show', compact('admin'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Admin $admin)
    {
        $branches = Branch::all();
        return view('admins.edit', compact('admin', 'branches'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Admin $admin)
    {
        // Validate the form inputs
        $request->validate([
            'name' => 'required|string|min:8',
            'email' => 'required|email|unique:admins,email,' . $admin->id,
            'phone' => 'nullable|string|min:10',
            'password' => 'nullable|string|min:6', // Optional password
            'branch_id' => 'required|array',
            'branch_id.*' => 'exists:branches,id', // Ensure each selected branch exists
        ]);

        // Prepare data for updating, excluding the password
        $data = $request->except('_token', '_method', 'password', 'branch_id');

        // If a password is provided, hash it
        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->input('password'));
        }

        // Update the admin data
        $admin->update($data);

        // Sync the branches (updates pivot table)
        $admin->branches()->sync($request->branch_id);

        return redirect()->route('admins.index')->with('update', 'Staff-Member Updated Successfully');
    }





    public function updateProfilePicture(Request $request, Admin $admin)
    {
        // Validate the incoming request
        $request->validate([
            'profile_img' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Check if a profile image is uploaded
        if ($request->hasFile('profile_img')) {
            $imageName = time() . '.' . $request->profile_img->extension();
            $request->profile_img->move(('images/admins'), $imageName);
        }

        $admin->update(['profile_img' => $imageName]);

        // Redirect back with success message
        return redirect()->route('admins.show', $admin)->with('add', 'Profile Picture Added Successfully');
    }

    public function updateBonus(Request $request)
    {
        // Update staff bonuses
        $staffBonuses = $request->input('staff', []);
        foreach ($staffBonuses as $staffId => $data) {
            DB::table('staff_salaries')
                ->where('id', $staffId)
                ->update(['bonus' => $data['bonus']]);
        }

        // Update admin bonuses
        $adminBonuses = $request->input('admin', []);
        foreach ($adminBonuses as $adminSalaryId => $data) {
            DB::table('admin_salaries')
                ->where('id', $adminSalaryId)
                ->update(['bonus' => $data['bonus']]);
        }

        return redirect()->back()->with('update', 'Bonuses updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Admin $admin)
    {
        $admin->delete();
        return redirect()->route('admins.index')->with('delete', 'Staff-Member Deleted Successfully');
    }
}
