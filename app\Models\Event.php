<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Scopes\BranchScope; 
use App\Services\BranchManager;

class Event extends Model
{
    use HasFactory;

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    protected static function booted(): void
    {
        // Apply the global scope
        static::addGlobalScope(new BranchScope);

        // Automatically set branch_id when creating new records for this model
        static::creating(function ($model) {
            // Get BranchManager instance
            $branchManager = app(BranchManager::class); 

            // Only set if branch context exists AND branch_id isn't already being set manually
            if ($branchManager->isBranchContextSet() && is_null($model->branch_id)) {
                $model->branch_id = $branchManager->getBranchId(); 
            }
        });
    }
}
