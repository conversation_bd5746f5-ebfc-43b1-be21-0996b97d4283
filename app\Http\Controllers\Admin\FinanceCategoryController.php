<?php


namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Http\Requests\Admin\FinanceCategoryRequest;
use App\Models\FinanceCategory;
use Illuminate\Http\Request;

class FinanceCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $type = request()->input('type', 'all');
        $financeCategories = FinanceCategory::orderBy('created_at', 'desc');

        if ($search) {
            $financeCategories = $financeCategories->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('type', 'like', '%' . $search . '%');
            });
        }

        if ($type !== 'all') {
            $financeCategories = $financeCategories->where('type', $type);
        }

        confirmDelete('Delete Category!', "Are you sure you want to delete this category?");

        return view('finance_categories.index', [
            'financeCategories' => $financeCategories->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'type' => $type,
        ]);
        
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('finance_categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FinanceCategoryRequest $request)
    {
        FinanceCategory::create($request->except('_token'));
        return redirect()->route('finance_categories.index')->with('add', 'Finance Category Created Successfully');
    }    

    /**
     * Display the specified resource.
     */
    public function show(FinanceCategory $financeCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FinanceCategory $financeCategory)
    {
        return view('finance_categories.edit', compact('financeCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FinanceCategoryRequest $request, FinanceCategory $financeCategory)
    {
        $financeCategory->update($request->except('_token', '_method'));
        return redirect()->route('finance_categories.index')->with('update', 'Finance Category Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FinanceCategory $financeCategory)
    {
        $financeCategory->delete();
        return redirect()->route('finance_categories.index')->with('delete', 'Finance Category Deleted Successfully');
    }
}
