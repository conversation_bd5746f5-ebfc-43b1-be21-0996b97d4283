<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Services\BranchManager;

class Enrollment extends Model
{
    use HasFactory;

    protected static function boot()
    {
        parent::boot();

        static::created(function ($enrollment) {
            $sessionCycles = SessionCycle::where('course_cycle_id', $enrollment->course_cycle_id)->get();

            foreach ($sessionCycles as $sessionCycle) {
                $sessionCycle->student_attendances()->create([
                    'user_id' => $enrollment->user_id,
                ]);
            }
        });

        static::deleted(function ($enrollment) {
            // Delete related student_attendances when an enrollment is deleted
            $sessionCycles = SessionCycle::where('course_cycle_id', $enrollment->course_cycle_id)->get();

            foreach ($sessionCycles as $sessionCycle) {
                StudentAttendance::where('session_cycle_id', $sessionCycle->id)
                    ->where('user_id', $enrollment->user_id)
                    ->delete();
            }
        });
    }

    protected static function booted()
    {
        static::deleting(function ($enrollment) {
            foreach ($enrollment->payments as $payment) {
                $payment->delete(); // This triggers Payment::deleting, which handles cash cleanup
            }
        });
    }
    public function getCourseRemainingAttribute()
    {
        $coursePriceAfterDiscount = $this->course_cycle->level->course_price * 2 - $this->discount_value;

        return $coursePriceAfterDiscount - $this->course_payment_amount;
    }
    public function getMaterialRemainingAttribute()
    {
        if (!$this->course_cycle->level->course->has_material) {
            return null;
        }

        return $this->course_cycle->level->material_price - $this->material_payment_amount;
    }

    public function scopeForCurrentBranch($query)
    {
        $branchManager = app(BranchManager::class);

        if ($branchManager->isBranchContextSet()) {
            $branchId = $branchManager->getBranchId();

            $query->whereHas('course_cycle', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        }

        return $query;
    }
    public function course_cycle()
    {
        return $this->belongsTo(CourseCycle::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Recalculate and update the course and material payment amounts based on all payments
     */
    public function recalculatePaymentAmounts()
    {
        // Get all payments for this enrollment
        $payments = $this->payments;

        // Reset payment amounts
        $coursePaymentAmount = 0;
        $materialPaymentAmount = 0;

        // Calculate totals from all payments
        foreach ($payments as $payment) {
            if ($payment->type == 0) { // Course payment
                $coursePaymentAmount += $payment->amount;
            } else { // Material payment
                $materialPaymentAmount += $payment->amount;
            }
        }

        // Update the enrollment
        $this->course_payment_amount = $coursePaymentAmount;
        $this->material_payment_amount = $materialPaymentAmount;

        // Update payment statuses
        $this->updatePaymentStatuses();

        return $this;
    }

    /**
     * Update the course and material payment statuses based on current payment amounts
     */
    public function updatePaymentStatuses()
    {
        // Update course payment status
        $coursePriceAfterDiscount = $this->course_cycle->level->course_price * 2 - $this->discount_value;

        if ($this->course_payment_amount >= $coursePriceAfterDiscount) {
            $this->course_payment_status = 'full';
        } elseif ($this->course_payment_amount >= $coursePriceAfterDiscount / 2) {
            $this->course_payment_status = '1month';
        } elseif ($this->course_payment_amount > 0) {
            $this->course_payment_status = 'down_payment';
        } else {
            $this->course_payment_status = 'none';
        }

        // Update material payment status if the course has materials
        if ($this->course_cycle->level->course->has_material) {
            $materialPrice = $this->course_cycle->level->material_price;

            if ($this->material_payment_amount >= $materialPrice) {
                $this->material_payment_status = 'full';
            } elseif ($this->material_payment_amount >= $materialPrice / 2) {
                $this->material_payment_status = '1month';
            } elseif ($this->material_payment_amount > 0) {
                $this->material_payment_status = 'down_payment';
            } else {
                $this->material_payment_status = 'none';
            }
        }

        return $this;
    }
}
