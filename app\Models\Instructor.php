<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Sanctum\Contracts\HasAbilities;
use App\Scopes\BranchScope; 
use App\Services\BranchManager;

class Instructor extends Authenticatable
{
    use HasFactory, HasApiTokens;

    protected $guard = 'instructor';
    protected static function booted(): void
    {
        // Apply the global scope
        static::addGlobalScope(new BranchScope);

        // Automatically set branch_id when creating new records for this model
        static::creating(function ($model) {
            // Get BranchManager instance
            $branchManager = app(BranchManager::class); 

            // Only set if branch context exists AND branch_id isn't already being set manually
            if ($branchManager->isBranchContextSet() && is_null($model->branch_id)) {
                $model->branch_id = $branchManager->getBranchId(); 
            }
        });
    }
    // protected $casts = [

    // ];

    // protected $fillable = ['name', 'email', 'password', 'remember_token'];

    // protected $hidden = ['password', 'remember_token'];

    public function course_cycles()
    {
        return $this->hasMany(CourseCycle::class);
    }
    public function instructor_attendances()
    {
        return $this->hasMany(InstructorAttendance::class);
    }    
    public function instructor_salaries()
    {
        return $this->hasMany(InstructorSalary::class);
    }
    public function getAgeAttribute()
    {
        // Calculate the age based on the date of birth
        return $this->date_of_birth->age;
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }


}
