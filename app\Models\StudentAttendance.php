<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentAttendance extends Model
{
    use HasFactory;
    protected $casts = [
        'feedback_sent_at' => 'datetime',
    ];
    public function session_cycle()
    {
        return $this->belongsTo(SessionCycle::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
