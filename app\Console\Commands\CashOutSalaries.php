<?php

namespace App\Console\Commands;

use App\Models\AdminSalary;
use App\Models\CashOut;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\InstructorSalary;
use App\Models\StaffSalary;
use Illuminate\Console\Command;

class CashOutSalaries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cashout:salaries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate cash out for instructor, staff, and admin salaries';

    /**
     * Execute the console command.
     */
    public function __construct()
    {
        parent::__construct();
    }
    public function handle()
    {
        $previousMonthStart = Carbon::now()->subMonth()->startOfMonth()->format('Y-m-d');
        $previousMonthEnd = Carbon::now()->subMonth()->endOfMonth()->format('Y-m-d');


        $instructorTotal = InstructorSalary::where('month', $previousMonthStart)
            ->sum(DB::raw('salary + bonus + media_bonus'));

        $staffTotal = StaffSalary::where('month', $previousMonthStart)
            ->sum(DB::raw('salary + bonus'));


        $adminTotal = AdminSalary::where('month', $previousMonthStart)
            ->sum(DB::raw('salary + bonus'));

        // $totalAmount = $instructorTotal + $staffTotal;

        // CashOut::create([
        //     'finance_category_id' => 2,
        //     'date' => Carbon::now()->subMonth()->endOfMonth()->format('Y-m-d'),
        //     'amount' => $totalAmount,
        //     'note' => "Instructors and staff salaries"
        // ]);


        $this->updateOrCreateCashOut($instructorTotal, 7, 14, 'Instructor salaries', $previousMonthEnd);
        $this->updateOrCreateCashOut($staffTotal, 7, 15, 'Staff salaries', $previousMonthEnd);
        $this->updateOrCreateCashOut($adminTotal, 7, 15, 'Admin salaries', $previousMonthEnd);

        $this->info('Cash out recorded for instructor, staff, and admin salaries.');
    }

    private function updateOrCreateCashOut($amount, $categoryId, $subCategoryId, $note, $date)
    {
        if ($amount > 0) {
            CashOut::updateOrCreate(
                [
                    'finance_category_id' => $categoryId,
                    'finance_sub_category_id' => $subCategoryId,
                    'date' => $date
                ],
                [
                    'amount' => $amount,
                    'payment_way' => 'instapay',
                    'note' => "{$note} for {$date}"
                ]
            );
        }
    }
}
