<?php

namespace App\Exports;

use App\Models\Payment;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
class PaymentExport implements FromCollection, WithHeadings, WithMapping, WithTitle
{

    private $rowNumber = 1;

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Payment::where('amount', '>', 0)
                      ->orderBy('created_at', 'desc')
                      ->get();
    }



    /**
     * Headings for the Excel file.
     */
    public function headings(): array
    {
        return [
            '#',
            'Student',
            'Course',
            'Amount',
            'Type',
            'Date',
        ];
    }

    /**
     * Map each payment to an array of data for the export.
     */
    public function map($payment): array
    {
        $row=$this->rowNumber++;
        return [
            $row,
            $payment->enrollment->user->name ?? 'None',
            $payment->enrollment->course_cycle->level->course->name . ' - ' . $payment->enrollment->course_cycle->level->name,
            $payment->amount,
            $payment->type == 1 ? 'Material' : 'Course',
            $payment->date ?? 'N/A',
        ];
    }

    /**
     * Sheet title for the Excel file.
     */
    public function title(): string
    {
        return 'Payments' . now()->format('M Y');
    }
}
