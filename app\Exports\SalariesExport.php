<?php

namespace App\Exports;

use App\Models\InstructorSalary;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class SalariesExport implements FromCollection, WithHeadings, WithTitle
{
    protected $startOfMonth;
    protected $endOfMonth;
    protected $instructorName;

    public function __construct($startOfMonth, $endOfMonth, $instructorName = null)
    {
        $this->startOfMonth = $startOfMonth;
        $this->endOfMonth = $endOfMonth;
        $this->instructorName = $instructorName;
    }

    public function collection()
    {
        $salaries = InstructorSalary::whereBetween('created_at', [$this->startOfMonth, $this->endOfMonth])
            ->when($this->instructorName, function ($query) {
                return $query->whereHas('instructor', function ($query) {
                    $query->where('name', 'like', '%' . $this->instructorName . '%');
                });
            })
            ->with('instructor')
            ->get();

        return $salaries->map(function ($salary, $index) {
            return [
                'index' => $index + 1,
                'instructor' => $salary->instructor->name,
                'working_hours' => $salary->work_hours,
                'salary' => number_format($salary->salary, 2),
            ];
        });
    }

    public function headings(): array
    {
        return [
            '#',
            'Instructor',            
            'Working Hours',
            'Salary',
        ];
    }

    public function title(): string
    {
        $date = Carbon::parse($this->startOfMonth);
        return 'Instructor-Salaries ' . $date->format('M Y');
    }
}


