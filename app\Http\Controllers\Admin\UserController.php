<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StudentRequest;
use App\Http\Requests\Admin\UpdateStudentRequest;
use App\Jobs\SendStudentCreatedEmail;
use App\Models\Enrollment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $students = User::orderBy('status', 'desc');

        if ($search) {
            $students = $students
                ->whereAny(['name', 'phone', 'email', 'school'], 'like', '%' . request()->get('search', '') . '%');
        }

        confirmDelete('Delete Student!', "Are you sure you want to delete this student?");

        return view('students.index', [
            'students' => $students->paginate(12)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    public function getHumatAlwatanStudents()
    {
        $search = request()->input('search');
        $students = User::where('hezb', '=', 1)->orderBy('status', 'desc');

        if ($search) {
            $students = $students
                ->whereAny(['name', 'phone', 'email', 'school'], 'like', '%' . request()->get('search', '') . '%');
        }

        confirmDelete('Delete Student!', "Are you sure you want to delete this student?");

        return view('students.index', [
            'students' => $students->paginate(12)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('students.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StudentRequest $request)
    {
        $password = substr($request->email, 0, 4) . substr($request->phone, -4);
        $student = User::create($request->except('_token', '_method', 'password') +
            [
                'password' => Hash::make($password)
            ]);

        SendStudentCreatedEmail::dispatch($student, $password)->onQueue('mail_queue');

        return redirect()->route('students.index')->with('add', 'Student Added Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $student)
    {
        $courses = Enrollment::where('user_id', $student->id)->paginate(8);
        return view('students.show', compact('student', 'courses'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $student)
    {
        return view('students.edit', compact('student'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentRequest $request, User $student)
    {
        $data = $request->except('_token', '_method');

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->get('password'));
        } else {
            $data['password'] = $student->password;
        }

        $student->update($data);

        return redirect()->route('students.index')->with('update', 'Data of ' . $student->name . ' Updated Successfully');
    }

    /**
     * Update Studen  Profile Picture.
     */

    public function updateProfilePicture(Request $request, User $student)
    {
        // Validate the incoming request
        $request->validate([
            'profile_img' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Check if a profile image is uploaded
        if ($request->hasFile('profile_img')) {
            $imageName = time() . '.' . $request->profile_img->extension();
            $request->profile_img->move(('images/profiles'), $imageName);
        }

        $student->update(['profile_img' => $imageName]);

        // Redirect back with success message
        return redirect()->route('students.show', $student)->with('add', 'Profile Picture Added Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $student)
    {
        $student->delete();
        return redirect()->route('students.index')->with('delete', 'Student Deleted Successfully');
    }
}
