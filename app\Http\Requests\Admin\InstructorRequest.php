<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class InstructorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'min:8'],
            'email' => ['required', 'email', 'unique:instructors'],
            'password' => ['required', Password::min(8)],
            'phone' => ['required', 'digits:11', 'regex:/^(010|011|012)\d{8}$/', 'unique:instructors'],
            'date_of_birth' => ['required'],
            'city' => ['required'],
            'address' => ['required'],
            'gender' => ['required', 'in:male,female'],
            'date_of_join' => ['required'],
            'job_type' => ['required'],
            'variable_salary' => ['required'],
            'fixed_salary' => ['required'],
            'status' => ['required'],
        ];
    }
}
