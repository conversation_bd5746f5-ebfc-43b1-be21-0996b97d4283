<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CashIn;
use App\Models\CashOut;
use App\Models\Payment;
use Illuminate\Http\Request;
use Carbon\Carbon;

class FinanceReportsController extends Controller
{
    public function index(Request $request)
    {
        $monthYear = $request->query('month');
        if ($monthYear) {
            [$year, $month] = explode('-', $monthYear);
            $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth();
            $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth();
        } else {
            $startOfMonth = Carbon::now()->startOfMonth();
            $endOfMonth = Carbon::now()->endOfMonth();
        }

        // $startOfThisMonth = Carbon::now()->startOfMonth();
        // $endOfThisMonth = Carbon::now()->endOfMonth();


        // Charts data
        // ----------------
        $cashInsWithoutCoursesCash  = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'cash')
            ->where('finance_sub_category_id', '!=', 1)
            ->sum('amount');
        $cashInsWithoutCoursesInstapay  = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'instapay')
            ->where('finance_sub_category_id', '!=', 1)
            ->sum('amount');
        $cashInsWithoutCoursesVodafone  = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'vodafone_cash')
            ->where('finance_sub_category_id', '!=', 1)
            ->sum('amount');

        $coursesCashInsCash = CashIn::where('payment_way', 'cash')
            ->where('finance_sub_category_id', 1)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->sum('amount');

        $coursesCashInsInstapay = CashIn::where('payment_way', 'instapay')
            ->where('finance_sub_category_id', 1)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->sum('amount');

        $coursesCashInsVodafoneCash = CashIn::where('payment_way', 'vodafone_cash')
            ->where('finance_sub_category_id', 1)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->sum('amount');

        // Total CashIn this Month
        // ----------------
        $cashInsWithoutCourses = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])
            // ->whereIn('payment_way', ['cash', 'instapay', 'vodafone_cash'])
            ->where('finance_sub_category_id', '!=', 1)
            ->sum('amount');

        $coursesCashIns = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('finance_sub_category_id', 1)
            ->sum('amount');

        $totalCashInsThisMonth = $cashInsWithoutCourses + $coursesCashIns;

        // Total CashOut this Month
        // ----------------
        $totalCashOutsThisMonth = CashOut::whereBetween('date', [$startOfMonth, $endOfMonth])
            // ->whereIn('payment_way', ['cash', 'instapay', 'vodafone_cash'])
            ->sum('amount');

        // Total Profits this Month
        // ----------------
        $totalProfitsThisMonth = $totalCashInsThisMonth - $totalCashOutsThisMonth;


        // Total CashIn, CashOut and Profits payment-way:cash this Month
        // ----------------
        $totalCashPayment = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'cash')
            ->sum('amount');
        // $coursesCash = Payment::whereBetween('date', [$startOfMonth, $endOfMonth])
        //     ->where('payment_way', 'cash')
        //     ->sum('amount');
        // $totalCashPayment = $cashPaymentCashIns + $coursesCash;

        $cashPaymentCashOuts = CashOut::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'cash')->sum('amount');

        $totalCashProfits = $totalCashPayment - $cashPaymentCashOuts;

        // Total CashIn, CashOut and Profits payment-way:instapay this Month
        // ----------------
        $totalInstapayPayment = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'instapay')
            ->sum('amount');
        // $coursesInstapay = Payment::whereBetween('date', [$startOfMonth, $endOfMonth])->where('payment_way', 'instapay')->sum('amount');
        // $totalInstapayPayment = $instapayPaymentCashIns + $coursesInstapay;
        $instapayPaymentCashOuts = CashOut::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'instapay')
            ->sum('amount');

        $totalInstapayProfits = $totalInstapayPayment - $instapayPaymentCashOuts;


        // Total CashIn, CashOut and Profits payment-way:vodafonecash this Month
        // ----------------
        $totalVodafonePayment = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'vodafone_cash')
            ->sum('amount');
        // $coursesVodafone = Payment::whereBetween('date', [$startOfMonth, $endOfMonth])->where('payment_way', 'vodafone_cash')->sum('amount');
        // $totalVodafonePayment = $vodafonePaymentCashIns + $coursesVodafone;

        $vodafonePaymentCashOuts = CashOut::whereBetween('date', [$startOfMonth, $endOfMonth])
            ->where('payment_way', 'vodafone_cash')
            ->sum('amount');

        $totalVodafoneProfits = $totalVodafonePayment - $vodafonePaymentCashOuts;

        return view('finance_reports.index', compact(
            'cashInsWithoutCourses',
            'coursesCashIns',
            'totalCashInsThisMonth',
            'totalCashOutsThisMonth',
            'totalProfitsThisMonth',
            'totalCashPayment',
            'cashPaymentCashOuts',
            'totalCashProfits',
            'totalInstapayPayment',
            'instapayPaymentCashOuts',
            'totalInstapayProfits',
            'totalVodafonePayment',
            'vodafonePaymentCashOuts',
            'totalVodafoneProfits',
            'monthYear',
            'cashInsWithoutCoursesCash',
            'cashInsWithoutCoursesInstapay',
            'cashInsWithoutCoursesVodafone',
            'coursesCashInsCash',
            'coursesCashInsInstapay',
            'coursesCashInsVodafoneCash'
        ));
    }
}
