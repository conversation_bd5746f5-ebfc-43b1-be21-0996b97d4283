<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EnrollForm;
use Illuminate\Http\Request;

class EnrollFormController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $request_status = request()->input('status', 'all'); // Default to 'all' if no status is provided
        $enroll_forms = EnrollForm::orderBy('created_at', 'desc');

        if ($search) {
            $enroll_forms = $enroll_forms->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('phone', 'like', '%' . $search . '%')
                    ->orWhere('age', 'like', '%' . $search . '%')
                    ->orWhere('request_status', 'like', '%' . $search . '%'); // Updated to request_status
            });
        }

        // Filter by request_status if it's not 'all'
        if ($request_status !== 'all') {
            $enroll_forms = $enroll_forms->where('request_status', $request_status); // Updated to request_status
        }

        confirmDelete('Delete Enroll Request!', "Are you sure you want to delete this request?");

        return view('enroll_form.index', [
            'enroll_forms' => $enroll_forms->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'status' => $request_status, // Pass the request_status to the view
        ]);
    }



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(EnrollForm $enroll_form)
    {
        return view('enroll_form.show', compact('enroll_form'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EnrollForm $enroll_form)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EnrollForm $enroll_form)
    {
        $enroll_form->request_status = $request->request_status;
        $enroll_form->save();
        return redirect()->back()->with('success', 'Status Updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EnrollForm $enroll_form)
    {
        $enroll_form->delete();
        return redirect()->route('enroll_form.index')->with('delete', 'Request Deleted Successfully');
    }
}
