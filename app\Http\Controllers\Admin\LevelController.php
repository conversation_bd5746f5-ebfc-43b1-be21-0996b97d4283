<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Level;
use App\Models\Session;
use App\Models\SessionMaterial;
use Illuminate\Http\Request;

class LevelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $levels = Level::orderBy('created_at', 'desc');

        if ($search) {
            $levels = $levels
                ->where('name', 'like', '%' . request()->get('search', '') . '%');
        }

        confirmDelete('Delete Level!', "Are you sure you want to delete this level?");

        return view('levels.index', [
            'levels' => $levels->paginate(12)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }



    public function add($id)
    {
        $course = Course::findOrFail($id);   
        $numLevels = $course->levels()->count();

        $levelOptions = [];
        for ($i = 1; $i <= $numLevels + 1; $i++) {
            $levelName = "Level $i";
            $levelOptions[$levelName] = $levelName;
        }
        $existingLevels = $course->levels()->pluck('name')->toArray();
        return view('levels.create', compact('id', 'levelOptions', 'existingLevels', 'course'));
    }




    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate(Level::rules());
        Level::create($request->except('_token'));
        return redirect()->route('courses.index')->with('add', 'Level Added Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Level $level)
    {
        confirmDelete('Delete File!', "Are you sure you want to delete this file?");

        $sessions = Session::where('level_id', $level->id)->get();        
        $sessionIds = $sessions->pluck('id')->toArray();
        $materials = SessionMaterial::whereIn('session_id', $sessionIds)->get();
        return view('levels.show', compact('level', 'sessions', 'materials'));

    }



    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Level $level)
    {
        return view('levels.edit', compact('level'));

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Level $level)
    {
        $level->update($request->except('_token', '_method'));
        return redirect()->back()->with('update', $level->name.' Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Level $level)
    {
        $level->delete();
        return redirect()->route('levels.index')->with('delete', 'Level Deleted Successfully');

    }
}
