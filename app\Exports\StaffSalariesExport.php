<?php

namespace App\Exports;

use App\Models\StaffSalary;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class StaffSalariesExport implements FromCollection, WithHeadings, WithTitle
{
    protected $startOfMonth;
    protected $endOfMonth;
    protected $staffName;

    public function __construct($startOfMonth, $endOfMonth, $staffName = null)
    {
        $this->startOfMonth = $startOfMonth;
        $this->endOfMonth = $endOfMonth;
        $this->staffName = $staffName;
    }
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $salaries = StaffSalary::whereBetween('month', [$this->startOfMonth, $this->endOfMonth])
            ->when($this->staffName, function ($query) {
                return $query->whereHas('admin', function ($query) {
                    $query->where('name', 'like', '%' . $this->staffName . '%');
                });
            })
            ->with('admin')
            ->get();

        return $salaries->map(function ($salary, $index) {
            return [
                'index' => $index + 1,
                'staff' => $salary->admin->name,
                'salary' => $salary->salary,
                'bonus' => $salary->bonus,
                'total_salary' => $salary->salary + $salary->bonus,
            ];
        });
    }

    public function headings(): array
    {
        return [
            '#',
            'Staff Member',
            'Salary',
            'Bonus',
            'Total Salary'
        ];
    }

    public function title(): string
    {
        $date = Carbon::parse($this->startOfMonth);
        return 'Staff-Salaries ' . $date->format('M Y');
    }
}
