<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Session;
use App\Models\SessionMaterial;
use Illuminate\Http\Request;

class SessionMaterialController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $session = Session::find($request->session_id);
        $fileUploaded = false; 

        if ($request->hasFile('file_path')) {
            foreach ($request->file('file_path') as $file) {
                $originalFileName = time() . '-' . $file->getClientOriginalName();
                $file->move('materials/', $originalFileName);                
                SessionMaterial::create([
                    'session_id' => $request->session_id,
                    'file_path' => $originalFileName
                ]);
                $fileUploaded = true; 
            }
        }

        if ($fileUploaded) {
            return redirect()->back()->with('add', 'Material added successfully for ' . $session->level->name. ' ' . $session->name);
        } else {
            return redirect()->back()->with('error', 'No files uploaded.');
        }
    }


    /**
     * Display the specified resource.
     */
    public function show(SessionMaterial $sessionMaterial)
    {

    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SessionMaterial $sessionMaterial)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SessionMaterial $sessionMaterial)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SessionMaterial $sessionMaterial)
    {
        $sessionMaterial->delete();
        return redirect()->route('enrollments.index')->with('delete', 'Material Deleted Successfully');
    }
}
