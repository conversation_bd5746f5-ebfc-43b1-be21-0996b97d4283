<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567891',
            'profile_img' => 'male.png',
            'date_of_birth' => '2010-01-01',
            'city' => 'Portsaid',
            'address' => '23 July, 16A Building, Portsaid',
            'gender' => 'male',
            'date_of_join' => '2024-02-06',
            'school' => 'Portsaid Elementary',
            'mother_phone' => '01012345678',
            'father_phone' => '01123345678',
            'mother_job' => fake()->jobTitle(),
            'father_job' => fake()->jobTitle(),
            'status' => 1,
        ]);
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567892',
            'profile_img' => 'male.png',
            'date_of_birth' => '2013-08-11',
            'city' => 'Portsaid',
            'address' => '15 El Tawfeek Street, El Manakh',
            'gender' => 'male',
            'date_of_join' => '2024-03-07',
            'school' => 'Portsaid Elementary',
            'mother_phone' => '01012345679',
            'father_phone' => '01123345679',
            'mother_job' => fake()->jobTitle(),
            'father_job' => fake()->jobTitle(),
            'status' => 1,
        ]);
        User::create([
            'name' => 'Hala Mahmoud Ahmed',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567893',
            'profile_img' => 'female.png',
            'date_of_birth' => '2015-03-22',
            'city' => 'Portsaid',
            'address' => '30 El Gomhoria Street, Elsharq',
            'gender' => 'female',
            'date_of_join' => '2023-12-03',
            'school' => 'Portsaid Elementary',
            'mother_phone' => '01012345671',
            'father_phone' => '01123345671',
            'mother_job' => fake()->jobTitle(),
            'father_job' => fake()->jobTitle(),
            'status' => 1,
        ]);
        User::create([
            'name' => 'Said Mohamed Mohamed',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567899',
            'profile_img' => 'male.png',
            'date_of_birth' => '2017-12-09',
            'city' => 'Portsaid',
            'address' => '2 El Shohadaa Street, Al Manakh',
            'gender' => 'male',
            'date_of_join' => '2024-02-22',
            'school' => 'Portsaid Elementary',
            'mother_phone' => '01012345679',
            'father_phone' => '01123345679',
            'mother_job' => fake()->jobTitle(),
            'father_job' => fake()->jobTitle(),
            'status' => 1,
        ]);
        User::create([
            'name' => 'Amira Samir Mohamed',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567894',
            'profile_img' => 'female.png',
            'date_of_birth' => '2017-06-05',
            'city' => 'Portsaid',
            'address' => '3 El Gueish Street, Al Manakh',
            'gender' => 'female',
            'date_of_join' => '2024-01-02',
            'school' => 'Portsaid Elementary',
            'mother_phone' => '01012345674',
            'father_phone' => '01123345674',
            'mother_job' => fake()->jobTitle(),
            'father_job' => fake()->jobTitle(),
            'status' => 1,
        ]);
        User::create([
            'name' => 'Yara Mohamed Mahmoud',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567895',
            'profile_img' => 'female.png',
            'date_of_birth' => '2018-01-06',
            'city' => 'Portsaid',
            'address' => '12 El Naser Street, Al Manakh',
            'gender' => 'female',
            'date_of_join' => '2024-04-02',
            'school' => 'ElQanah Elementary',
            'mother_phone' => '01012345675',
            'father_phone' => '01123345675',
            'mother_job' => fake()->jobTitle(),
            'father_job' => fake()->jobTitle(),
            'status' => 1,
        ]);
        User::create([
            'name' => 'Amir Ahmed Mahmoud',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567890',
            'profile_img' => 'male.png',
            'date_of_birth' => '2012-03-15',
            'city' => 'Cairo',
            'address' => '22 El Maadi Street, Al Maadi',
            'gender' => 'male',
            'date_of_join' => '2024-05-01',
            'school' => 'Al Azhar Primary School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Teacher',
            'father_job' => 'Engineer',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Nour Mohamed Ali',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234557892',
            'profile_img' => 'female.png',
            'date_of_birth' => '2014-07-21',
            'city' => 'Alexandria',
            'address' => '8 El Shatby Street, Al Shatby',
            'gender' => 'female',
            'date_of_join' => '2024-04-10',
            'school' => 'El Nasr Language School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Doctor',
            'father_job' => 'Lawyer',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Omar Hassan Ali',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234467893',
            'profile_img' => 'male.png',
            'date_of_birth' => '2011-09-05',
            'city' => 'Giza',
            'address' => '15 Sphinx Street, Haram',
            'gender' => 'male',
            'date_of_join' => '2024-03-20',
            'school' => 'Nile International School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Engineer',
            'father_job' => 'Doctor',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Leila Mahmoud Ibrahim',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234467894',
            'profile_img' => 'female.png',
            'date_of_birth' => '2014-11-11',
            'city' => 'Aswan',
            'address' => '30 Nubian Street, Nubia',
            'gender' => 'female',
            'date_of_join' => '2024-04-05',
            'school' => 'Aswan Experimental School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Teacher',
            'father_job' => 'Architect',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Youssef Ali Ibrahim',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234569895',
            'profile_img' => 'male.png',
            'date_of_birth' => '2017-05-20',
            'city' => 'Luxor',
            'address' => '5 Karnak Street, Karnak',
            'gender' => 'male',
            'date_of_join' => '2024-05-03',
            'school' => 'Luxor International School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Doctor',
            'father_job' => 'Engineer',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Mohamed Hassan Mahmoud',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '***********',
            'profile_img' => 'male.png',
            'date_of_birth' => '2011-04-25',
            'city' => 'Suez',
            'address' => '18 Suez Canal Street, Suez',
            'gender' => 'male',
            'date_of_join' => '2024-05-08',
            'school' => 'Suez National School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Teacher',
            'father_job' => 'Businessman',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Fatma Mahmoud Ali',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '***********',
            'profile_img' => 'female.png',
            'date_of_birth' => '2015-06-30',
            'city' => 'Portsaid',
            'address' => '10 Suez Street, Port Fouad',
            'gender' => 'female',
            'date_of_join' => '2024-04-25',
            'school' => 'Portsaid International School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Lawyer',
            'father_job' => 'Engineer',
            'status' => 0,
        ]);
        User::create([
            'name' => 'Ahmed Mohamed Hassan',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '***********',
            'profile_img' => 'male.png',
            'date_of_birth' => '2010-12-03',
            'city' => 'Damietta',
            'address' => '7 Nile Street, New Damietta',
            'gender' => 'male',
            'date_of_join' => '2024-05-10',
            'school' => 'Damietta Experimental School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Teacher',
            'father_job' => 'Engineer',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Sara Mahmoud Ali',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234557001',
            'profile_img' => 'female.png',
            'date_of_birth' => '2016-06-20',
            'city' => 'Fayoum',
            'address' => '3 Oasis Street, Tunis',
            'gender' => 'female',
            'date_of_join' => '2024-04-30',
            'school' => 'Fayoum Language School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Doctor',
            'father_job' => 'Lawyer',
            'status' => 0,
        ]);
        User::create([
            'name' => 'Ali Hassan Ibrahim',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01233567002',
            'profile_img' => 'male.png',
            'date_of_birth' => '2015-09-15',
            'city' => 'Qena',
            'address' => '12 Luxor Street, New Qena',
            'gender' => 'male',
            'date_of_join' => '2024-05-05',
            'school' => 'Qena International School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Engineer',
            'father_job' => 'Doctor',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Lina Ahmed Mahmoud',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234567303',
            'profile_img' => 'female.png',
            'date_of_birth' => '2014-03-10',
            'city' => 'Sohag',
            'address' => '20 Nile Street, New Sohag',
            'gender' => 'female',
            'date_of_join' => '2024-04-20',
            'school' => 'Sohag National School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Teacher',
            'father_job' => 'Architect',
            'status' => 0,
        ]);
        User::create([
            'name' => 'Mustafa Ali Mahmoud',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234537004',
            'profile_img' => 'male.png',
            'date_of_birth' => '2013-11-25',
            'city' => 'Beni Suef',
            'address' => '6 Cairo Street, New Beni Suef',
            'gender' => 'male',
            'date_of_join' => '2024-05-02',
            'school' => 'Beni Suef Experimental School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Doctor',
            'father_job' => 'Engineer',
            'status' => 0,
        ]);
        User::create([
            'name' => 'Aisha Mohamed Ibrahim',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '01234537005',
            'profile_img' => 'female.png',
            'date_of_birth' => '2016-08-12',
            'city' => 'Asyut',
            'address' => '9 Nile Street, New Asyut',
            'gender' => 'female',
            'date_of_join' => '2024-04-18',
            'school' => 'Asyut International School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Engineer',
            'father_job' => 'Pilot',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Youssef Mahmoud Ali',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '***********',
            'profile_img' => 'male.png',
            'date_of_birth' => '2017-06-30',
            'city' => 'Kafr El Sheikh',
            'address' => '15 Nile Street, New Kafr El Sheikh',
            'gender' => 'male',
            'date_of_join' => '2024-05-07',
            'school' => 'Kafr El Sheikh National School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Teacher',
            'father_job' => 'Businessman',
            'status' => 1,
        ]);
        User::create([
            'name' => 'Khaled Ali Hassan',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '***********',
            'profile_img' => 'male.png',
            'date_of_birth' => '2018-01-20',
            'city' => 'Assiut',
            'address' => '12 Nile Street, New Assiut',
            'gender' => 'male',
            'date_of_join' => '2024-02-06',
            'school' => 'Assiut International School',
            'mother_phone' => '***********',
            'father_phone' => '***********',
            'mother_job' => 'Doctor',
            'father_job' => 'Engineer',
            'status' => 1,
        ]);
    }
}
