<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enroll_forms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone');
            $table->string('age');
            $table->text('city')->nullable();
            $table->text('address')->nullable();
            $table->string('school')->nullable();
            $table->boolean('status')->default(0)->nullable();
            $table->boolean('prereq')->default(0)->nullable();
            $table->foreignId('course_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->enum('request_status',['pending','accepted','rejected'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enroll_forms');
    }
};
