<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TrackRequest;
use App\Http\Requests\Admin\UpdateTrackRequest;
use App\Models\Track;
use Illuminate\Http\Request;

class TrackController extends Controller
{
    public function index()
    {
        $search = request()->input('search');
        $tracks = Track::orderBy('created_at', 'desc');

        if($search) {
            $tracks = $tracks
            ->where('name','like', '%'. request()->get('search', '') . '%');
        }

        confirmDelete( 'Delete Track!', "Are you sure you want to delete this track?");

        return view('tracks.index', [
            'tracks' => $tracks->paginate(12)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('tracks.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TrackRequest $request)
    {

        Track::create($request->except('_token') );

        return redirect()->route('tracks.index')->with('add', 'Track Added Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Track $track)
    {
        return view('tracks.show', compact('track'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Track $track)
    {
        return view('tracks.edit', compact('track'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTrackRequest $request, Track $track)
    {
        $track->update($request->except('_token', '_method'));

        return redirect()->route('tracks.index')->with('update', 'Track '.$track->name. ' Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Track $track)
    {
        $track->delete();
        return redirect()->route('tracks.index')->with('delete', 'Track Deleted Successfully');
    }
}
