<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\BranchManager; // <-- Use the new class name
use App\Models\Branch;
use Illuminate\Support\Facades\URL;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;


class IdentifyBranch
{
    // Inject the BranchManager singleton
    public function __construct(protected BranchManager $branchManager) {}

    public function handle(Request $request, Closure $next): Response
    {
        // --- Identify Branch based on Origin Header ---
        // $origin = $request->header('Origin');
        // $host = $request->getHost();
        $branch = null;

        // if ($origin) {
        // $host = parse_url($origin, PHP_URL_HOST);
        $host = $request->getHost();

        if ($host) {
            $parts = explode('.', $host);
            // Adjust logic based on your domain structure (e.g., needs 3 parts like sub.domain.com)
            $isPotentialSubdomain = (count($parts) >= 3);

            // Ensure it's not www or the API subdomain itself
            if ($isPotentialSubdomain && !in_array($parts[0], ['www', 'api', config('app.api_subdomain')])) { // Example: exclude 'api'
                $subdomainSlug = $parts[0];
                $branch = Branch::where('subdomain', $subdomainSlug)
                    // ->where('is_active', true)
                    ->first();
            }
        }
        // }

        // --- Abort if no valid branch identified ---
        // Add any routes that *don't* need branch context here
        // if ($this->shouldBypassBranchCheck($request)) {
        //     return $next($request);
        // }
        if (!$branch) {
            if (count(explode('.', $host)) <= 2) {
                return $next($request);
            }

            throw new NotFoundHttpException('Branch not found.');
        }

        // --- Set Branch Context using BranchManager ---
        $this->branchManager->setBranch($branch); // <-- Use the injected BranchManager

        // Optional: Set default URL parameters if your routes use {branch_slug}
        // URL::defaults(['branch_slug' => $branch->subdomain_slug]);

        return $next($request);
    }

    // Optional helper function example
    // protected function shouldBypassBranchCheck(Request $request): bool
    // {
    //     $publicRoutes = ['login', 'password.request', 'api.public.info']; // Example route names
    //     return $request->routeIs(...$publicRoutes);
    // }
}
