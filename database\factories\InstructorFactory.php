<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Instructor>
 */
class InstructorFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'remember_token' => Str::random(10),
            'city' => fake()->city(),
            'address' => fake()->address(),
            'date_of_birth' => fake()->date(),
            'date_of_join' => fake()-> date(),
            'salary' => fake()->randomDigitNotZero(),
            'phone' => fake()->phoneNumber(),
            'work_hours' => fake()->randomDigit(),
            'job_type' => fake()->randomElement(['part_time', 'full_time']),
            'status' => fake()->numberBetween(0,1),
        ];
    }
}
