<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CashIn;
use App\Models\CashOut;
use App\Models\Balance;
use Carbon\Carbon;
use Illuminate\Http\Request;

class BalanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $balances = Balance::orderBy('type')->paginate(10);
        return view('balances.index', compact('balances'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Balance $balance)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Balance $balance)
    {
        return view('balances.edit', compact('balance'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Balance $balance)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
        ]);

        $balance->update([
            'amount' => $request->amount,
        ]);

        return redirect()->route('balances.index')->with('update', 'Balance updated successfully.');
    }
    public function showTransfersPage()
    {
        $balances = Balance::all();

        return view('balances.balances_transfers', compact('balances'));
    }



    public function transfer(Request $request)
    {
        $request->validate([
            'from_account' => 'required|different:to_account',
            'to_account' => 'required',
            'amount' => 'required|numeric|min:0.01',
        ]);

        $fromAccount = $request->from_account;
        $toAccount = $request->to_account;
        $amount = $request->amount;
        $branchId = auth()->user()->branch_id;

        // Deduct from the sender
        CashOut::create([
            'payment_way' => $fromAccount,
            'amount' => $amount,
            'branch_id' => $branchId,
            'date' => Carbon::now(),
            'finance_category_id' => 22,
            'note' => "Account transaction: Transferred to $toAccount balance.",
        ]);

        // Add to the receiver
        CashIn::create([
            'payment_way' => $toAccount,
            'amount' => $amount,
            'branch_id' => $branchId,
            'date' => Carbon::now(),
            'finance_category_id' => 22,
            'note' => "Account transaction: Transferred from $fromAccount balance.",
        ]);

        return redirect()->route('balances.index')->with('update', 'Transfer completed successfully.');
    }



    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Balance $balance)
    {
        //
    }
}
