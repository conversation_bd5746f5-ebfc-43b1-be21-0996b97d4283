<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Models\Profit;
use Illuminate\Http\Request;

class ProfitController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $year = request()->input('year');
        $startDate = request()->input('start_date');
        $endDate = request()->input('end_date');
        $amount = request()->input('amount');

        $profits = Profit::orderBy('created_at', 'desc');

        if ($search) {
            $profits = $profits->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%');
            });
        }

        if ($year) {
            $profits = $profits->whereYear('date', $year);
        }

        if ($startDate) {
            $profits = $profits->whereDate('date', '>=', $startDate);
        }

        if ($endDate) {
            $profits = $profits->whereDate('date', '<=', $endDate);
        }

        if ($amount) {
            $profits = $profits->where('amount', '>=', $amount);
        }

        return view('profits.index', [
            'profits' => $profits->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'year' => $year,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'amount' => $amount,
        ]);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Profit $profit)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Profit $profit)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Profit $profit)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Profit $profit)
    {
        //
    }
}
