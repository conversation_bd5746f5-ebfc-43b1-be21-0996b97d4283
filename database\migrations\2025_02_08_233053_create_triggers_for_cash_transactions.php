<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Trigger for cash_ins
        DB::unprepared("
            CREATE TRIGGER after_cash_in_insert
            AFTER INSERT ON cash_ins
            FOR EACH ROW
            BEGIN
                DECLARE balance_before DOUBLE;
                DECLARE balance_after DOUBLE;

                SELECT amount INTO balance_before 
                FROM balances 
                WHERE type = NEW.payment_way AND branch_id = NEW.branch_id;

                SET balance_after = IFNULL(balance_before, 0) + NEW.amount;

                INSERT INTO transactions (type, balance_before, balance_after, reference_table, reference_id, created_at, updated_at)
                VALUES (NEW.payment_way, balance_before, balance_after, 'cash_ins', NEW.id, NOW(), NOW());

                INSERT INTO balances (type, branch_id, amount, created_at, updated_at)
                VALUES (NEW.payment_way, NEW.branch_id, balance_after, NOW(), NOW())
                ON DUPLICATE KEY UPDATE amount = balance_after;
            END;
        ");

        // Trigger for cash_outs
        DB::unprepared("
            CREATE TRIGGER after_cash_out_insert
            AFTER INSERT ON cash_outs
            FOR EACH ROW
            BEGIN
                DECLARE balance_before DOUBLE;
                DECLARE balance_after DOUBLE;

                SELECT amount INTO balance_before 
                FROM balances 
                WHERE type = NEW.payment_way AND branch_id = NEW.branch_id;

                SET balance_after = IFNULL(balance_before, 0) - NEW.amount;

                INSERT INTO transactions (type, balance_before, balance_after, reference_table, reference_id, created_at, updated_at)
                VALUES (NEW.payment_way, balance_before, balance_after, 'cash_outs', NEW.id, NOW(), NOW());

                INSERT INTO balances (type, branch_id, amount, created_at, updated_at)
                VALUES (NEW.payment_way, NEW.branch_id, balance_after, NOW(), NOW())
                ON DUPLICATE KEY UPDATE amount = balance_after;
            END;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared("DROP TRIGGER IF EXISTS after_cash_in_insert;");
        DB::unprepared("DROP TRIGGER IF EXISTS after_cash_out_insert;");
    }
};
