<?php

namespace Database\Seeders;

use App\Models\Enrollment;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Enrollment::create([
            'user_id' => 1,
            'course_cycle_id' => 1,
            'course_payment_amount' => 100,
            'material_payment_amount' => 100,
            'course_payment_status' => 'full',
            'material_payment_status' => 'full',
            'payment_way' => 'cash',
            'graduation_status' => 'not_certified',
            'enroll_date' => '2024-5-15',
            'total_price' => 500,

        ]);
        Enrollment::create([
            'user_id' => 2,
            'course_cycle_id' => 1,
            'course_payment_amount' => 200,
            'material_payment_amount' => 0,
            'course_payment_status' => 'full',
            'material_payment_status' => 'none',
            'payment_way' => 'cash',
            'graduation_status' => 'not_certified',
            'enroll_date' => '2024-5-15',
            'total_price' => 500,
        ]);
        Enrollment::create([
            'user_id' => 1,
            'course_cycle_id' => 2,
            'course_payment_amount' => 500,
            'material_payment_amount' => 500,
            'course_payment_status' => 'full',
            'material_payment_status' => 'full',
            'payment_way' => 'cash',
            'graduation_status' => 'certified',
            'enroll_date' => '2024-5-15',
            'total_price' => 500,
        ]);
        Enrollment::create([
            'user_id' => 4,
            'course_cycle_id' => 1,
            'course_payment_amount' => 100,
            'material_payment_amount' => 100,
            'course_payment_status' => 'full',
            'material_payment_status' => 'full',
            'payment_way' => 'cash',
            'graduation_status' => 'not_certified',
            'enroll_date' => '2024-5-15',
            'total_price' => 500,
        ]);
        Enrollment::create([
            'user_id' => 3,
            'course_cycle_id' => 1,
            'course_payment_amount' => 100,
            'material_payment_amount' => 100,
            'course_payment_status' => 'full',
            'material_payment_status' => 'full',
            'payment_way' => 'cash',
            'graduation_status' => 'not_certified',
            'enroll_date' => '2024-5-15',
            'total_price' => 500,

        ]);
        Enrollment::create([
            'user_id' => 3,
            'course_cycle_id' => 2,
            'course_payment_amount' => 100,
            'material_payment_amount' => 100,
            'course_payment_status' => 'full',
            'material_payment_status' => 'full',
            'payment_way' => 'cash',
            'graduation_status' => 'not_certified',
            'enroll_date' => '2024-5-15',
            'total_price' => 500,

        ]);
    }
}
