<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EnrollmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        return [
            'user_id' => [
                'required',
                'exists:users,id',
                Rule::unique('enrollments')->where(function ($query) {
                    return $query->where('course_cycle_id', $this->course_cycle_id);
                }),
            ],
            'course_cycle_id' => 'required|exists:course_cycles,id',
            'enroll_date' => 'required|date|before_or_equal:today',
            // 'course_payment_amount' => 'required|numeric|max:' . $this->get('total_price'),
            'course_payment_status' => 'required|in:none,1month,full,down_payment',
            'material_payment_status' => 'nullable|in:none,1month,full,down_payment',
            'graduation_status' => [
                'required',
                Rule::in(['not_certified', 'certified']),
                function ($attribute, $value, $fail) {
                    if (
                        $value === 'certified' &&
                        ($this->course_payment_status !== 'full')
                    ) {
                        $fail('The course cannot be certified until both course and material payment statuses are full.');
                    }
                },
            ],
            'payment_way' => [
                function ($attribute, $value, $fail) {
                    $coursePaymentAmount = $this->input('course_payment_amount');
                    $materialPaymentAmount = $this->input('material_payment_amount');


                    if ($coursePaymentAmount == 0 && $materialPaymentAmount == 0 && $value !== 'none') {
                        $fail('Payment way must be none if both course payment amount and material payment amount are 0.');
                    }
            
                    if ($coursePaymentAmount > 0 && $materialPaymentAmount > 0 && $value === 'none') {
                        $fail('Payment way cannot be none if both course payment amount and material payment amount are greater than 0.');
                    }
                },
            ],
        ];
    }

    public function messages()
    {
        return [
            'user_id.unique' => 'This user is already enrolled in the selected course cycle.',
            // 'course_payment_amount.max' => 'The course payment amount cannot be greater than the total price.',
            'graduation_status.in' => 'The course cannot be certified until course payment status is full.',
        ];
    }
}
