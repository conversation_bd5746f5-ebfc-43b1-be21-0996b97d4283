<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Scopes\BranchScope; 
use App\Services\BranchManager;

class CourseCycle extends Model
{
    use HasFactory;

    protected static function boot()
    {
        parent::boot();

        // Apply the branch global scope
        static::addGlobalScope(new BranchScope);

        // Automatically set the branch_id when creating
        static::creating(function ($model) {
            $branchManager = app(BranchManager::class);
            if ($branchManager->isBranchContextSet() && is_null($model->branch_id)) {
                $model->branch_id = $branchManager->getBranchId();
            }
        });

        // Handle session cycle creation after a course cycle is created
        static::created(function ($courseCycle) {
            $level = $courseCycle->level;
            if (!$level) return;

            $sessions = $level->sessions()->take(8)->get();
            $startDate = Carbon::parse($courseCycle->start_date);

            $sessionsData = [];
            foreach ($sessions as $index => $session) {
                $sessionsData[] = [
                    'course_cycle_id' => $courseCycle->id,
                    'session_id' => $session->id,
                    'date' => $startDate->copy()->addDays($index * 7),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            DB::table('session_cycles')->insert($sessionsData);
        });

        // Handle updates to session cycles
        static::updated(function ($courseCycle) {
            if ($courseCycle->isDirty('start_date')) {
                $startDate = Carbon::parse($courseCycle->start_date);
                $sessions = $courseCycle->level->sessions()->take(8)->get();

                foreach ($sessions as $index => $session) {
                    $sessionCycle = $courseCycle->sessionCycles()->where('session_id', $session->id)->first();
                    if ($sessionCycle) {
                        $sessionCycle->date = $startDate->copy()->addDays($index * 7);
                        $sessionCycle->save();
                    }
                }
            }

            if ($courseCycle->isDirty('end_date')) {
                $lastSession = $courseCycle->sessionCycles()->orderBy('date', 'desc')->first();
                if ($lastSession) {
                    $lastSession->date = Carbon::parse($courseCycle->end_date);
                    $lastSession->save();
                }
            }
        });
    }


    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    public function cycle()
    {
        return $this->belongsTo(Cycle::class);
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function instructor()
    {
        return $this->belongsTo(Instructor::class);
    }

    public function sessionCycles()
    {
        return $this->hasMany(SessionCycle::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }
}

