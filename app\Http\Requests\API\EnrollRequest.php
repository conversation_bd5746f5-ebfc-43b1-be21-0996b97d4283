<?php

namespace App\Http\Requests\API;

use App\Http\Requests\API\FormRequest;

class EnrollRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'=>'required|string|min:3',
            'email' => 'required|string|email',
            'phone' => ['required', 'digits:11', 'regex:/^(010|011|012)\d{8}$/'],
            'age' => 'required|numeric|between:5,20',
            'address' => 'required|string|min:5',
            'school' => 'required|string|min:5',
            'status' => 'nullable|boolean',
            'prereq' => 'nullable|boolean',
            'course_id' => 'required|numeric|exists:courses,id',
        ];
    }
}
