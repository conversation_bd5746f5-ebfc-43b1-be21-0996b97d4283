<?php

namespace Database\Seeders;

use App\Models\Branch;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Branch::create([
            'name' => 'Port Said',
            'subdomain' => 'port-said',
            'phone' => '01091788466',
            'address' => 'Portsaid- Memphis St, Portsaid Governorate, Egypt'
        ]);

        Branch::create([
            'name' => 'AlMansoura',
            'subdomain' => 'almansoura',
            'phone' => '01044960408',
            'address' => 'Mansoura - University District - Above Shelter Restaurant, next to Awad Serag'
        ]);
    }
}
