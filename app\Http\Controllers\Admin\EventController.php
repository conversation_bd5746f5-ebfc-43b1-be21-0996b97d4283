<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;

use App\Http\Requests\Admin\EventRequest;
use App\Http\Requests\Admin\UpdateEventRequest;
use App\Models\Event;
use Illuminate\Http\Request;

class EventController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $events = Event::orderBy('created_at', 'desc');

        if($search) {
            $events = $events
            ->where('name','like', '%'. request()->get('search', '') . '%');
        }

        confirmDelete( 'Delete Event!', "Are you sure you want to delete this event?");

        return view('events.index', [
            'events' => $events->paginate(12)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('events.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(EventRequest $request)
    {
        if ($request->hasFile('image')) {
            $ImageName = time() . '.' . $request->image->extension();
            $request->image->move(('images/events/'), $ImageName);
        }

        Event::create($request->except('image', '_token') +
            ['image' => $ImageName]);

        return redirect()->route('events.index')->with('add', 'Event Added Successfully');

    }

    /**
     * Display the specified resource.
     */
    public function show(Event $event)
    {
        return view('events.show', compact('event'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Event $event)
    {
        return view('events.edit', compact('event'));

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateEventRequest $request, Event $event)
    {
        $event->update($request->except('image', '_token', '_method'));

        if ($request->hasFile('image')) {
            $ImageName = time() . '.' . $request->image->extension();
            $request->image->move(('images/events/'), $ImageName);
            $event->update(['image' => $ImageName]);
        }
        return redirect()->route('events.index')->with('update', 'Event Updated Successfully');

    }

    public function updateImage(Request $request, Event $event)
    {
        // Validate the incoming request
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Check if a profile image is uploaded
        if ($request->hasFile('image')) {
            $imageName = time() . '.' . $request->image->extension();
            $request->image->move(('images/events'), $imageName);
        }

        $event->update(['image' => $imageName]);

        // Redirect back with success message
        return redirect()->route('events.show', $event)->with('add', 'Profile Picture Added Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Event $event)
    {
        $event->delete();
        return redirect()->route('events.index')->with('delete', 'Event Deleted Successfully');

    }
}
