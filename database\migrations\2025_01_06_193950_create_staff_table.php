<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('staff', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('branch_id')->nullable()->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->string('email')->unique();
            $table->string('password');
            $table->string('profile_img')->nullable();
            $table->string('phone')->unique()->nullable();
            $table->date('date_of_birth')->nullable();
            $table->string('city')->nullable();
            $table->text('address')->nullable();
            $table->enum('gender',['male','female'])->nullable();
            $table->decimal('salary')->nullable()->default(0);
            $table->decimal('bonus')->nullable()->default(0);
            $table->enum('job_type',['full_time','part_time'])->nullable();
            $table->integer('work_hours')->nullable()->default(0);
            $table->date('date_of_join')->nullable();
            $table->boolean('status')->default(1)->nullable();
            $table->enum('role',['it_manager','pr_member','media','pr_manager','hr','designer','administrative_manager','general_secretary'])->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('staff');
    }
};
