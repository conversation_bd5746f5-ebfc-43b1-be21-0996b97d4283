<?php

namespace App\Models;

use App\Services\BranchManager;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\CashIn;
use App\Models\CashOut;

class Payment extends Model
{
    use HasFactory;

    protected static function booted()
    {
        static::deleting(function ($payment) {
            // Prevent deleting cash if it's a force delete without soft delete setup (optional check)
            $relatedCashIn = $payment->findRelatedCashIn();
            $relatedCashOut = $payment->findRelatedCashOut();
            $isNegative = $payment->amount < 0;

            if ($payment->type == 0) {
                // Course payment
                if ($isNegative && $relatedCashOut) {
                    $relatedCashOut->delete();
                } elseif (!$isNegative && $relatedCashIn) {
                    $relatedCashIn->delete();
                }
            } else {
                // Material payment
                if ($isNegative && $relatedCashOut) {
                    $relatedCashOut->delete();
                } elseif (!$isNegative && $relatedCashIn) {
                    $relatedCashIn->delete();
                }
            }

            // Optional: Recalculate payment amounts
            if ($payment->enrollment) {
                $payment->enrollment->recalculatePaymentAmounts();
                $payment->enrollment->save();
            }
        });
    }


    public function scopeForCurrentBranch($query)
    {
        $branchManager = app(BranchManager::class);

        if ($branchManager->isBranchContextSet()) {
            $branchId = $branchManager->getBranchId();

            // Filter payments that belong to enrollments associated with the branch
            $query->whereHas('enrollment.course_cycle', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        }

        return $query;
    }

    protected $casts = [
        'date' => 'datetime',
    ];

    public function enrollment()
    {
        return $this->belongsTo(Enrollment::class);
    }

    /**
     * Get the cash_in record associated with this payment
     * This is a helper method to find the cash_in record that was created when this payment was made
     */
    public function findRelatedCashIn()
    {
        $date = $this->date->format('Y-m-d');
        $amount = $this->amount;
        $paymentWay = $this->payment_way;
        $paymentType = $this->type;

        // Get student name for more specific matching
        $studentName = $this->enrollment->user->name ?? '';

        // Get course name for more specific matching
        $courseName = $this->enrollment->course_cycle->level->course->name ?? '';

        // Find cash_in records that match this payment's details
        $query = CashIn::where('date', 'like', "{$date}%")
            ->where('amount', $amount)
            ->where('payment_way', $paymentWay);

        // Add type-specific note filtering
        if ($paymentType == 0) { // Course payment
            $query->where(function ($q) use ($studentName, $courseName) {
                $q->where('note', 'like', "student's course payment%");

                // If we have student name, add it to the filter
                if (!empty($studentName)) {
                    $q->orWhere('note', 'like', "%{$studentName}%course payment%");
                }

                // If we have course name, add it to the filter
                if (!empty($courseName)) {
                    $q->orWhere('note', 'like', "%{$courseName}%payment%");
                }
            });
        } else { // Material payment
            $query->where(function ($q) use ($studentName, $courseName) {
                $q->where('note', 'like', "student's material payment%");

                // If we have student name, add it to the filter
                if (!empty($studentName)) {
                    $q->orWhere('note', 'like', "%{$studentName}%material payment%");
                }

                // If we have course name, add it to the filter
                if (!empty($courseName)) {
                    $q->orWhere('note', 'like', "%{$courseName}%material%");
                }
            });
        }

        // Order by created_at to get the most recent match
        // Limit to records created within 5 minutes of the payment
        $paymentCreatedAt = $this->created_at;
        if ($paymentCreatedAt) {
            $fiveMinutesBefore = (clone $paymentCreatedAt)->subMinutes(5);
            $fiveMinutesAfter = (clone $paymentCreatedAt)->addMinutes(5);

            $query->whereBetween('created_at', [$fiveMinutesBefore, $fiveMinutesAfter]);
        }

        return $query->orderBy('created_at', 'desc')->first();
    }

    /**
     * Get the cash_out record associated with this payment
     * This is a helper method to find the cash_out record that was created when this payment was refunded
     */
    public function findRelatedCashOut()
    {
        $date = $this->date->format('Y-m-d');
        $amount = abs($this->amount);
        $paymentWay = $this->payment_way;
        $paymentType = $this->type;

        // Get student name for more specific matching
        $studentName = $this->enrollment->user->name ?? '';

        // Get course name for more specific matching
        $courseName = $this->enrollment->course_cycle->level->course->name ?? '';

        // Find cash_out records that match this payment's details
        $query = CashOut::where('date', 'like', "{$date}%")
            ->where('amount', $amount)
            ->where('payment_way', $paymentWay);

        // Add type-specific note filtering
        if ($paymentType == 0) { // Course payment
            $query->where(function ($q) use ($studentName, $courseName) {
                $q->where('note', 'like', "student's course payment refund%")
                    ->orWhere('note', 'like', "student's course payment deletion%");

                // If we have student name, add it to the filter
                if (!empty($studentName)) {
                    $q->orWhere('note', 'like', "%{$studentName}%course%refund%")
                        ->orWhere('note', 'like', "%{$studentName}%course%deletion%");
                }

                // If we have course name, add it to the filter
                if (!empty($courseName)) {
                    $q->orWhere('note', 'like', "%{$courseName}%refund%")
                        ->orWhere('note', 'like', "%{$courseName}%deletion%");
                }
            });
        } else { // Material payment
            $query->where(function ($q) use ($studentName, $courseName) {
                $q->where('note', 'like', "student's material payment refund%")
                    ->orWhere('note', 'like', "student's material payment deletion%");

                // If we have student name, add it to the filter
                if (!empty($studentName)) {
                    $q->orWhere('note', 'like', "%{$studentName}%material%refund%")
                        ->orWhere('note', 'like', "%{$studentName}%material%deletion%");
                }

                // If we have course name, add it to the filter
                if (!empty($courseName)) {
                    $q->orWhere('note', 'like', "%{$courseName}%material%refund%")
                        ->orWhere('note', 'like', "%{$courseName}%material%deletion%");
                }
            });
        }

        // Order by created_at to get the most recent match
        // Limit to records created within 5 minutes of the payment
        $paymentCreatedAt = $this->created_at;
        if ($paymentCreatedAt) {
            $fiveMinutesBefore = (clone $paymentCreatedAt)->subMinutes(5);
            $fiveMinutesAfter = (clone $paymentCreatedAt)->addMinutes(5);

            $query->whereBetween('created_at', [$fiveMinutesBefore, $fiveMinutesAfter]);
        }

        return $query->orderBy('created_at', 'desc')->first();
    }
}
