<?php


namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CashOutRequest;
use App\Models\CashOut;
use App\Models\FinanceCategory;
use App\Models\FinanceSubCategory;
use Illuminate\Http\Request;

class CashOutController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $search = $request->input('search');
        $category = $request->input('category');
        $subCategory = $request->input('sub_category');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $cash_outs = CashOut::orderBy('date', 'desc');

        // Apply search filter
        if ($search) {
            $cash_outs = $cash_outs->where(function ($query) use ($search) {
                $query->where('amount', 'like', '%' . $search . '%')
                    ->orWhere('date', 'like', '%' . $search . '%')
                    ->orWhere('note', 'like', '%' . $search . '%');
            });
        }

        // Apply category filter
        if ($category) {
            $cash_outs = $cash_outs->where('finance_category_id', $category);
        }

        // Apply sub-category filter
        if ($subCategory) {
            $cash_outs = $cash_outs->where('finance_sub_category_id', $subCategory);
        }

        // Apply date filter
        if ($startDate && $endDate) {
            $cash_outs = $cash_outs->whereBetween('date', [$startDate, $endDate]);
        }

        confirmDelete('Delete Cash-Out!', "Are you sure you want to delete this cash-out?");

        return view('cash_outs.index', [
            'cash_outs' => $cash_outs->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'category' => $category,
            'subCategory' => $subCategory,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'categories' => FinanceCategory::whereIn('type', ['both', 'cash_out'])->get(),
            'subCategories' => FinanceSubCategory::whereHas('finance_category', function ($query) {
                $query->whereIn('type', ['both', 'cash_out']);
            })->get(),
        ]);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $financeCategories = FinanceCategory::whereIn('type', ['both', 'cash_out'])->get();
        $financeSubCategories = FinanceSubCategory::all();
        return view('cash_outs.create', compact('financeCategories', 'financeSubCategories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CashOutRequest $request)
    {
        CashOut::create($request->except('_token'));
        return redirect()->route('cash_outs.index')->with('add', 'Cash-Out Created Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(CashOut $cashOut)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CashOut $cashOut)
    {
        $financeCategories = FinanceCategory::whereIn('type', ['both', 'cash_out'])->get();
        $financeSubCategories = FinanceSubCategory::all();
        return view('cash_outs.edit', compact('cashOut', 'financeCategories', 'financeSubCategories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CashOutRequest $request, CashOut $cashOut)
    {
        $cashOut->update($request->except('_token', '_method'));
        return redirect()->route('cash_outs.index')->with('update', 'Cash-Out Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CashOut $cashOut)
    {
        $cashOut->delete();
        return redirect()->route('cash_outs.index')->with('delete', 'Cash-Out Deleted Successfully');
    }
}
