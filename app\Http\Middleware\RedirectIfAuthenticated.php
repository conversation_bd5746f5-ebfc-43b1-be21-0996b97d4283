<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Services\BranchManager; // <-- Use the new class name
use App\Models\User;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;
        $branchManager = app(BranchManager::class); // <-- Get BranchManager instance

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                // User is logged in. Now check if they belong to the current branch context.
                /** @var User $user */
                $user = Auth::guard($guard)->user();
                $currentBranchId = $branchManager->getBranchId();

                // Proceed with redirect ONLY if user belongs to the current branch
                if ($branchManager->isBranchContextSet() && $user->branch_id === $currentBranchId) {
                    // Redirect to dashboard within the current branch subdomain
                    return redirect(RouteServiceProvider::HOME);
                } else {
                    // User is logged in, but NOT to the branch matching the current subdomain.
                    // Option A: Log them out of the mismatched session and let them see the login page. (Safer)
                    Auth::guard($guard)->logout();
                    // Don't invalidate session here, just remove auth for this guard
                    // Allow the request to proceed (which will likely hit the login page)
                    // return $next($request); // Let request continue to intended route (likely login)

                    // Option B: Redirect to their *actual* branch home (More complex)
                    // if ($user->branch) { // Assumes branch relationship exists
                    //    $correctSubdomain = $user->branch->subdomain_slug;
                    //    $baseDomain = config('app.domain', 'yourdomain.com');
                    //    return redirect()->to('https://' . $correctSubdomain . '.' . $baseDomain . RouteServiceProvider::HOME);
                    // } else {
                    // Fallback: Log out if we can't determine their correct branch
                    Auth::guard($guard)->logout();
                    //    return $next($request);
                    // }

                    // Sticking with Option A (Logout and proceed) for simplicity:
                    Auth::guard($guard)->logout();
                    // $request->session()->invalidate(); // Maybe too aggressive? Let's just logout guard.
                    // $request->session()->regenerateToken();
                    // Let the request proceed to the login page of the *current* subdomain
                    // return $next($request);
                    // --- OR --- Redirect explicitly to the login page of current subdomain
                    // This might cause a redirect loop if not careful. Let's try proceeding first.
                    // Fallback: Just redirect to generic home/login if context is weird
                    // return redirect('/');
                }
            }
        }
        return $next($request);
    }
}
