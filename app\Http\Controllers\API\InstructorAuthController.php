<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\InstructorLoginRequest;
use App\Http\Requests\API\InstructorRegisterRequest;
use App\Models\Instructor;
use Illuminate\Support\Facades\Hash;


class InstructorAuthController extends Controller
{
    /**
     * @OA\Post(
     *      path="/instructor_register",
     *      operationId="instructor_register",
     *      tags={"Instructor"},
     *      summary="Instructor Register",
     *      description="Instructor Register",
     *     @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(mediaType="multipart/form-data",
     *              @OA\Schema(
     *                  required={"name","email","password","phone"},
     *                  @OA\Property(
     *                      property="name",
     *                      type="string",
     *                      description="Instructor Name"
     *                  ),
     *                  @OA\Property(
     *                      property="email",
     *                      type="string",
     *                      description="Instructor Email"
     *                  ),
     *                  @OA\Property(
     *                      property="password",
     *                      type="string",
     *                      format="password",
     *                      description="Instructor Password"
     *                  ),
     *                  @OA\Property(
     *                      property="phone",
     *                      type="string",
     *                      description="Instructor Phone"
     *                  ),
     *             )
     *         )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function register(InstructorRegisterRequest $request)
    {
        Instructor::create([
            'name' => $request->get('name'),
            'email' => $request->get('email'),
            'password' => Hash::make($request->get('password')),
            'date_of_birth' => $request->get('date_of_birth'),
            'phone' => $request->get('phone'),
        ]);
        return response()->json([
            'message' => 'Instructor Created Successfully',
        ]);
    }

    /**
     * @OA\Post(
     *      path="/instructor_login",
     *      operationId="instructor_login",
     *      tags={"Instructor"},
     *      summary="Instructor Login",
     *      description="Instructor Login",
     *     @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(mediaType="multipart/form-data",
     *              @OA\Schema(
     *                  required={"email","password"},
     *                  @OA\Property(
     *                      property="email",
     *                      type="string",
     *                      description="Instructor Email"
     *                  ),
     *                  @OA\Property(
     *                      property="password",
     *                      type="string",
     *                      format="password",
     *                      description="Instructor Password"
     *                  ),
     *             )
     *         )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */

    public function login(InstructorLoginRequest $request)
    {
        $user = Instructor::where('email', $request->get('email'))->first();
        if (!$user) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid Credentials'
            ], 401);
        } elseif (!Hash::check($request->get('password'), $user->password)) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid Credentials'
            ], 401);
        }

        if (auth('instructor')->check()) {
            auth('instructor')->user()->tokens()->delete();
        }

        $token = $user->createToken($user->name . '-AuthToken')->plainTextToken;
        return response()->json([
            'status' => true,
            'message' => 'Instructor Logged In Successfully',
            'access_token' => $token,
            'instructor_name' => $user->name,
            'instructor_img' => asset('https://admin.mtechsquare.com/images/instructors/' . $user->profile_img)
        ]);
    }
    /**
     * @OA\Post(
     *      path="/instructor_logout",
     *      operationId="instructor_logout",
     *      tags={"Instructor"},
     *      summary="Instructor Loogout",
     *      description="Instructor Loogout",
     *      security={ {"sanctum": {} }},
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function logout()
    {
        $instructor = auth('instructor')->user();

        if ($instructor) {
            $instructor->currentAccessToken()->delete();
        }

        return response()->json([
            "message" => "Instructor logged out Successfully"
        ]);
    }
}
