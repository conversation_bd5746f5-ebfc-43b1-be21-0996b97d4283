<?php

namespace App\Providers;

use App\Models\Contact;
use App\Models\CourseCycle;
use App\Models\EnrollForm;
use App\Models\SessionCycle;
use App\Models\StudentFeedback;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\Relation;
use App\Models\CashIn;
use App\Models\CashOut;
use App\Services\BranchManager;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */ public function register(): void
    {
        // Register BranchManager as a singleton
        $this->app->singleton(BranchManager::class, function ($app) {
            return new BranchManager();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Relation::morphMap([
            'cash_ins' => CashIn::class,
            'cash_outs' => CashOut::class,
        ]);

        Model::unguard();
        Paginator::useBootstrapFive();
        View::composer('*', function ($view) {
            $pendingContactsCount = Contact::where('status', 'pending')->count();
            $pendingEnrollRequestsCount = EnrollForm::where('request_status', 'pending')->count();
            $pendingFeedbacksCount = StudentFeedback::where('feedback_status', 'pending')->count();

            $startOfMonth = Carbon::now()->startOfMonth()->toDateString();
            $today = Carbon::today();
            $courseCyclesInProgress = CourseCycle::whereIn('course_status', ['in_progress', 'completed'])
                ->pluck('id');

            $todaySessionCycles = SessionCycle::whereIn('course_cycle_id', $courseCyclesInProgress)
                ->whereBetween('date', [$startOfMonth, $today])
                ->pluck('id');

            $unsubmittedSessionsCount = SessionCycle::whereIn('id', $todaySessionCycles)
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('instructor_attendances')
                        ->whereColumn('instructor_attendances.session_cycle_id', 'session_cycles.id');
                })
                ->count();
            $studentsAttendanceCount = SessionCycle::whereIn('id', $todaySessionCycles)
                ->where('students_attendance', 0)->count();

            $view->with('pendingContactsCount', $pendingContactsCount)
                ->with('pendingEnrollRequestsCount', $pendingEnrollRequestsCount)
                ->with('pendingFeedbacksCount', $pendingFeedbacksCount)
                ->with('unsubmittedSessionsCount', $unsubmittedSessionsCount)
                ->with('studentsAttendanceCount', $studentsAttendanceCount);
        });
    }
}
