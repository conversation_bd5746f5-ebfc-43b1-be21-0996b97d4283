<?php

namespace App\Http\Requests\Admin;

use App\Rules\UniqueCaseInsensitive;
use Illuminate\Foundation\Http\FormRequest;

class UpdateEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $eventId = $this->route('event')->id ?? null;
        return [
            'title' => ['required', 'min:3', new UniqueCaseInsensitive('events', 'title', $eventId)],
            'date' => 'required|date|before_or_equal:today',
            'description' => 'required|min:3',
            'image' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }
}
