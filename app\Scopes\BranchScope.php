<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use App\Services\BranchManager; // <-- Use the new class name
use Illuminate\Support\Facades\App;

class BranchScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        // Get the BranchManager instance from the container
        $branchManager = App::make(BranchManager::class); // <-- Use BranchManager

        // Don't apply scope if running in console or if no branch context is set
        if (App::runningInConsole() || !$branchManager->isBranchContextSet()) { // <-- Use BranchManager method
            return;
        }

        $branchId = $branchManager->getBranchId(); // <-- Use BranchManager method

        if ($branchId) {
            // Prefix with table name to avoid ambiguity in joins
            $builder->where($model->getTable() . '.branch_id', $branchId);
        } else {
            // Fallback if context *should* be set but somehow isn't (prevents data leak)
             $builder->whereRaw('1 = 0'); // Force no results
            // Optionally throw an exception if this state is invalid
            // throw new \RuntimeException('Branch context required but Branch ID not found.');
        }
    }
}