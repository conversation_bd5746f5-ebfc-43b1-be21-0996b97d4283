<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\EnrollmentRequest;
use App\Http\Requests\Admin\UpdateEnrollmentRequest;
use App\Models\CashIn;
use App\Models\CashOut;
use App\Models\Course;
use App\Models\CourseCycle;
use App\Models\Enrollment;
use App\Models\Payment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Validator;


class EnrollmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $courseId = request()->input('course');
        $startDate = request()->input('start_date');
        $endDate = request()->input('end_date');
        $courses = CourseCycle::all();
        $enrollments = Enrollment::forCurrentBranch()->orderBy('created_at', 'desc');
        // $enrollments = Enrollment::orderBy('created_at', 'desc');
        if ($search) {
            $enrollments = $enrollments
                ->where(function ($query) use ($search) {
                    $query->where('material_payment_status', 'like', '%' . $search . '%')
                        ->orWhere('course_payment_status', 'like', '%' . $search . '%')
                        ->orWhere('status', 'like', '%' . $search . '%')
                        ->orWhere('enroll_date', 'like', '%' . $search . '%')
                        ->orWhere('grade', 'like', '%' . $search . '%')
                        ->orWhereHas('user', function ($q) use ($search) {
                            $q->where('name', 'like', '%' . $search . '%');
                        });
                });
        }
        if ($courseId) {
            $enrollments = $enrollments->where('course_cycle_id', $courseId);
        }
        if ($startDate && $endDate) {
            $enrollments = $enrollments->whereBetween('enroll_date', [$startDate, $endDate]);
        } elseif ($startDate) {
            $enrollments = $enrollments->where('enroll_date', '>=', $startDate);
        } elseif ($endDate) {
            $enrollments = $enrollments->where('enroll_date', '<=', $endDate);
        }
        confirmDelete('Delete Enrollement!', "Are you sure you want to delete this enrollement?");

        // Get enrollments with pagination
        $paginatedEnrollments = $enrollments->paginate(24)->appends(request()->except('page'));

        // Recalculate payment amounts for all enrollments on the current page
        // foreach ($paginatedEnrollments as $enrollment) {
        //     $enrollment->recalculatePaymentAmounts()->save();
        // }

        return view('enrollments.index', [
            'enrollments' => $paginatedEnrollments,
            'search' => $search,
            'courses' => $courses,
        ]);
    }

    public function getNotPaidEnrollments()
    {
        $search = request()->input('search');
        $enrollments = Enrollment::forCurrentBranch()->where(function ($query) {
            $query->where('status', '=', 1)
                ->where('course_payment_status', '!=', 'full')
                ->orWhere('material_payment_status', '!=', 'full');
        })
            ->with(['user', 'course_cycle.level.course'])
            ->get()
            ->filter(function ($enrollment) {
                $courseRemaining = $enrollment->courseRemaining;

                $materialRemaining = $enrollment->course_cycle->level->course->has_material
                    ? $enrollment->materialRemaining
                    : null;

                return $courseRemaining > 0 || ($materialRemaining !== null && $materialRemaining > 0);
            });
        if ($search) {
            $enrollments = $enrollments->filter(function ($enrollment) use ($search) {
                return $search && stripos($enrollment->user->name, $search) !== false;
            });
        }

        $perPage = 15;
        $currentPage = LengthAwarePaginator::resolveCurrentPage();
        $currentItems = $enrollments->slice(($currentPage - 1) * $perPage, $perPage)->all();
        $paginatedEnrollments = new LengthAwarePaginator($currentItems, $enrollments->count(), $perPage, $currentPage, [
            'path' => LengthAwarePaginator::resolveCurrentPath(),
        ]);

        // Recalculate payment amounts for all enrollments on the current page
        foreach ($paginatedEnrollments as $enrollment) {
            $enrollment->recalculatePaymentAmounts();
        }
        return view('enrollments.unpaid', [
            'enrollments' => $paginatedEnrollments,
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */

    public function enroll($id)
    {
        $student = User::find($id)->name;
        $enrolledCourseCycleIds = Enrollment::where('user_id', $id)
            ->pluck('course_cycle_id')
            ->toArray();
        $course_cycles = CourseCycle::with('level')
            ->where('course_status', '!=', ['completed', 'on_hold'])
            ->whereNotIn('id', $enrolledCourseCycleIds)
            ->get();
        return view('enrollments.create', compact('course_cycles', 'id', 'student'));
    }



    /**
     * Store a newly created resource in storage.
     */

    public function store(EnrollmentRequest $request)
    {
        $enrollment = Enrollment::create($request->except('_token'));

        $enrollDate = Carbon::parse($enrollment->enroll_date);
        $currentTime = Carbon::now()->toTimeString(); // Current time in 'HH:MM:SS' format
        $enrollDateTime = Carbon::parse("{$enrollDate->toDateString()} {$currentTime}");

        $totalPaymentAmount = 0;

        if ($enrollment->course_payment_amount > 0) {
            Payment::create([
                'enrollment_id' => $enrollment->id,
                'date' =>  $enrollDateTime,
                'amount' => $enrollment->course_payment_amount,
                'type' => 0, // Type 0 for course payment
                'payment_way' => $enrollment->payment_way,
            ]);
            $totalPaymentAmount += $enrollment->course_payment_amount;

            CashIn::create([
                'finance_category_id' => 1,
                'finance_sub_category_id' => 1,
                'date' => $enrollDateTime,
                'amount' => $enrollment->course_payment_amount,
                'payment_way' => $enrollment->payment_way,
                // 'note' => "student's course payment",
                'note' => "student's course payment for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
            ]);
        }

        if ($enrollment->material_payment_amount > 0) {
            Payment::create([
                'enrollment_id' => $enrollment->id,
                'date' => $enrollDateTime,
                'amount' => $enrollment->material_payment_amount,
                'type' => 1, // Type 1 for material payment
                'payment_way' => $enrollment->payment_way,
            ]);
            $totalPaymentAmount += $enrollment->material_payment_amount;

            CashIn::create([
                'finance_category_id' => 1,
                'finance_sub_category_id' => 1,
                'date' => $enrollDateTime,
                'amount' => $enrollment->material_payment_amount,
                'payment_way' => $enrollment->payment_way,
                // 'note' => "student's material payment",
                'note' => "student's material payment for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
            ]);
        }

        // if ($totalPaymentAmount > 0) {
        //     $cashInRecord = CashIn::where('finance_category_id', 1)
        //         ->where('finance_sub_category_id', 1)
        //         ->where('note', "student's payment for course and materials")
        //         ->whereBetween('date', [$monthStart, $monthEnd])
        //         ->first();

        //     if ($cashInRecord) {
        //         $cashInRecord->update([
        //             'amount' => $cashInRecord->amount + $totalPaymentAmount
        //         ]);
        //     } else {
        //         CashIn::create([
        //             'finance_category_id' => 1,
        //             'finance_sub_category_id' => 1,
        //             'date' => $enrollDateTime,
        //             'amount' => $totalPaymentAmount,
        //             'note' => "student's payment for course and materials",
        //         ]);
        //     }
        // }

        return redirect()->route('enrollments.index')->with('add', 'Enrollment Created Successfully');
    }


    /**
     * Display the specified resource.
     */
    public function show(Enrollment $enrollment)
    {
        // Recalculate payment amounts to ensure they're up to date
        // $enrollment->recalculatePaymentAmounts()->save();

        $student = User::find($enrollment->user_id);
        $course = CourseCycle::find($enrollment->course_cycle_id);
        $course_price = $course->level->course_price;
        $material_price = $course->level->material_price;
        $payments = Payment::where('enrollment_id', $enrollment->id)->get();

        return view('enrollments.show', compact('enrollment', 'student', 'course', 'course_price', 'material_price', 'payments'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Enrollment $enrollment)
    {
        // Recalculate payment amounts to ensure they're up to date
        // $enrollment->recalculatePaymentAmounts()->save();

        $student = User::find($enrollment->user_id);
        $course = CourseCycle::find($enrollment->course_cycle_id);
        $course_price = $course->level->course_price;
        $material_price = $course->level->material_price;
        $track = $course->level->course->track->name;
        $payments = Payment::where('enrollment_id', $enrollment->id)->get();

        return view('enrollments.edit', compact('enrollment', 'student', 'course', 'course_price', 'material_price', 'track', 'payments'));
    }

    /**
     * Update the specified resource in storage.
     */

    public function update(UpdateEnrollmentRequest $request, Enrollment $enrollment)
    {
        // Update enrollment details except payment amounts
        $enrollment->update($request->except('_token', '_method', 'amount'));
        

        return redirect()->route('enrollments.index')->with('update', 'Enrollment Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Enrollment $enrollment)
    {
        $enrollment->delete();
        return redirect()->route('enrollments.index')->with('delete', 'Enrollment Deleted Successfully');
    }
}
