<?php

namespace Database\Seeders;

use App\Models\Balance;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BalanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentTypes = ['cash', 'instapay', 'vodafone_cash', 'bank_account'];

        foreach ($paymentTypes as $type) {
            Balance::updateOrCreate(
                ['type' => $type],
                ['amount' => 0, 'branch_id' => 1]
            );
        }
    }
}
