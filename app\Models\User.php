<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Scopes\BranchScope; // Import the scope
use App\Services\BranchManager;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
    protected static function booted(): void
    {
        // Apply the global scope
        static::addGlobalScope(new BranchScope);

        // Automatically set branch_id when creating new records for this model
        static::creating(function ($model) {
            // Get BranchManager instance
            $branchManager = app(BranchManager::class); // <-- Use BranchManager

            // Only set if branch context exists AND branch_id isn't already being set manually
            if ($branchManager->isBranchContextSet() && is_null($model->branch_id)) {
                $model->branch_id = $branchManager->getBranchId(); // <-- Use BranchManager
            }

            // Optional: Add validation - throw exception if branch_id is required but missing
            // if (is_null($model->branch_id) && $branchManager->isBranchContextSet()) {
            //    throw new \InvalidArgumentException(class_basename($model) . ' requires a branch_id.');
            // }
        });
    }

    /**
     * Define the relationship to the Branch (adjust if many-to-many)
     */

    protected $guard = 'student';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    // protected $fillable = [
    //     'name',
    //     'email',
    //     'password',
    // ];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    public function student_attendances()
    {
        return $this->hasMany(StudentAttendance::class);
    }

    public function student_feedbacks()
    {
        return $this->hasMany(StudentFeedback::class);
    }

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    // protected $hidden = [
    //     'password',
    //     'remember_token',
    // ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    // protected $casts = [
    //     'email_verified_at' => 'datetime',
    // ];

}
