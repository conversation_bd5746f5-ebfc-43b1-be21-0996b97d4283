<?php


namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Http\Requests\Admin\FinanceSubCategoryRequest;
use App\Models\FinanceCategory;
use App\Models\FinanceSubCategory;
use Illuminate\Http\Request;

class FinanceSubCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $type = request()->input('type', 'all');
        $financeSubCategories = FinanceSubCategory::orderBy('created_at', 'desc');

        if ($search) {
            $financeSubCategories = $financeSubCategories->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%');
            });
        }

        if ($type !== 'all') {
            $financeSubCategories = $financeSubCategories->whereHas('finance_category', function ($query) use ($type) {
                $query->where('type', $type);
            });
        }

        confirmDelete('Delete Sub-Category!', "Are you sure you want to delete this sub-category?");

        return view('finance_sub_categories.index', [
            'financeSubCategories' => $financeSubCategories->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'type' => $type,
        ]);        
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $financeCategories = FinanceCategory::all();
        return view('finance_sub_categories.create', compact('financeCategories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FinanceSubCategoryRequest $request)
    {
        FinanceSubCategory::create($request->except('_token'));
        return redirect()->route('finance_sub_categories.index')->with('add', 'Finance Sub-Category Created Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(FinanceSubCategory $financeSubCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FinanceSubCategory $financeSubCategory)
    {
        $financeCategories = FinanceCategory::all();
        return view('finance_sub_categories.edit', compact('financeSubCategory','financeCategories'));

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FinanceSubCategoryRequest $request, FinanceSubCategory $financeSubCategory)
    {
        $financeSubCategory->update($request->except('_token', '_method'));
        return redirect()->route('finance_sub_categories.index')->with('update', 'Finance Sub-Category Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FinanceSubCategory $financeSubCategory)
    {
        $financeSubCategory->delete();
        return redirect()->route('finance_sub_categories.index')->with('delete', 'Finance Sub-Category Deleted Successfully');
    }
}
