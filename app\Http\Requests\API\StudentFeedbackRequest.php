<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class StudentFeedbackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'feedback' => 'required|string',  
        ];
    }
    
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (!\App\Models\SessionCycle::where('id', $this->route('session_cycle_id'))->exists()) {
                $validator->errors()->add('session_cycle_id', 'The selected session cycle does not exist.');
            }
        });
    }
    
}
