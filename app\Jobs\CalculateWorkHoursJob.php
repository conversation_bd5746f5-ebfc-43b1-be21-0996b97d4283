<?php

namespace App\Jobs;

use App\Models\InstructorAttendance;
use App\Models\InstructorSalary;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculateWorkHoursJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    // public $queue = 'work_hours_queue';

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $currentMonthStart = Carbon::now()->startOfMonth()->format('Y-m-d H:i:s');
        $currentMonthEnd = Carbon::now()->endOfMonth()->endOfDay()->format('Y-m-d H:i:s');

        $attendances = InstructorAttendance::whereBetween('created_at', [
            $currentMonthStart,
            $currentMonthEnd
        ])->get();

        $workHoursByInstructor = $attendances->groupBy('instructor_id');

        foreach ($workHoursByInstructor as $instructorId => $attendances) {
            $totalWorkHours = $attendances->count() * 2; 
            InstructorSalary::updateOrCreate(
                [
                    'instructor_id' => $instructorId,
                    'month' => $currentMonthStart, 
                ],
                [
                    'work_hours' => $totalWorkHours,
                ]
            );
        }
        InstructorSalary::where('work_hours', 0)->delete();
    }
    
}
