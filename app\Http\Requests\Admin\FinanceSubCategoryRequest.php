<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\UniqueCaseInsensitive;

class FinanceSubCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $financeSubCategoryId = $this->route('finance_sub_category') ? $this->route('finance_sub_category')->id : null;

        return [
            'name' => ['required', 'min:3', new UniqueCaseInsensitive('finance_sub_categories', 'name', $financeSubCategoryId)],
            'finance_category_id' => 'required|exists:finance_categories,id',
        ];
    }
}
