<?php

namespace App\Http\Controllers\Admin;

use App\Exports\AnalysisExport;
use App\Exports\CashInExport;
use App\Exports\CashOutExport;
use App\Exports\CommentsExport;
use App\Exports\GradesExport;
use App\Exports\InstructorSalariesDetailsExport;
use App\Exports\SalariesExport;
use App\Exports\StaffSalariesExport;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;


class ExportController extends Controller
{
    public function exportComments(Request $request)
    {
        $monthYear = $request->query('month');
        if (!$monthYear) {
            return redirect()->back()->withErrors(['month' => 'You need to choose a month to export comments.']);
        }
        [$year, $month] = explode('-', $monthYear);
        $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth();
        $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth();

        return Excel::download(new CommentsExport($startOfMonth, $endOfMonth), 'instructors-comments-' . now()->format('m_Y') . '.xlsx');
    }

    public function exportGrades(Request $request)
    {
        $monthYear = $request->query('month');
        if (!$monthYear) {
            return redirect()->back()->withErrors(['month' => 'You need to choose a month to export grades.']);
        }
        [$year, $month] = explode('-', $monthYear);
        $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth();
        $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth();

        $studentNameFilter = $request->query('student_name', ''); 
        $courseCycleFilter = $request->query('course_cycle_name', ''); 

        return Excel::download(new GradesExport($startOfMonth, $endOfMonth, $studentNameFilter, $courseCycleFilter), 'students-grades-' . now()->format('m_Y') . '.xlsx');
    }

    public function exportSalaries(Request $request)
    {
        $monthYear = $request->query('month');
        if (!$monthYear) {
            return redirect()->back()->withErrors(['month' => 'You need to choose a month to export salaries.']);
        }
        $instructorNameFilter = $request->query('instructor_name', '');
        [$year, $month] = explode('-', $monthYear);
        $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth();
        $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth();
    
        return Excel::download(new SalariesExport($startOfMonth, $endOfMonth, $instructorNameFilter), 'instructors-salaries-' . now()->format('m_Y') . '.xlsx');
    }

    public function exportInstructorSalaries(Request $request)
    {
        $monthYear = $request->query('month');
        if (!$monthYear) {
            return redirect()->back()->withErrors(['month' => 'You need to choose a month to export salaries.']);
        }
        $instructorNameFilter = $request->query('instructor_name', '');
        [$year, $month] = explode('-', $monthYear);
        $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth();
        $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth();
    
        return Excel::download(new InstructorSalariesDetailsExport($startOfMonth, $endOfMonth, $instructorNameFilter), 'instructors-salaries-' . now()->format('m_Y') . '.xlsx');
    }
    public function exportStaffSalaries(Request $request)
    {
        $monthYear = $request->query('month');
        if (!$monthYear) {
            return redirect()->back()->withErrors(['month' => 'You need to choose a month to export salaries.']);
        }
        $staffNameFilter = $request->query('staff_name', '');
        [$year, $month] = explode('-', $monthYear);
        $startOfMonth = Carbon::create($year, $month, 1)->startOfMonth();
        $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth();
    
        return Excel::download(new StaffSalariesExport($startOfMonth, $endOfMonth, $staffNameFilter), 'staff-salaries-' . now()->format('m_Y') . '.xlsx');
    }

    public function exportAnalysisToExcel(Request $request)
    {        
        $newStudentsData = $request->input('newStudentsData'); 
        $newEnrollmentsData = $request->input('newEnrollmentsData');
        $newGroupsData = $request->input('newGroupsData');
        $studentsData = $request->input('studentsData');
        $enrollmentsData = $request->input('enrollmentsData');
        $groupsData = $request->input('groupsData');
        return Excel::download(
            new AnalysisExport($newStudentsData, $newEnrollmentsData, $newGroupsData, $studentsData, $enrollmentsData, $groupsData),
            'analysis-'. now()->format('m_Y') .'.xlsx'
        );
    }

    public function exportCashIns(Request $request){

        $startDate = $request->query('start_date');
        $endDate = $request->query('end_date');
        $category = $request->query('category');
        $subCategory = $request->query('sub_category');
        return Excel::download(new CashInExport($startDate, $endDate, $category,$subCategory), 'cash-ins-' . now()->format('m_Y') . '.xlsx');
    }

    public function exportCashOuts(Request $request){

        $startDate = $request->query('start_date');
        $endDate = $request->query('end_date');
        $category = $request->query('category');
        $subCategory = $request->query('sub_category');
        return Excel::download(new CashOutExport($startDate, $endDate, $category,$subCategory), 'cash-outs-' . now()->format('m_Y') . '.xlsx');
    }

}
