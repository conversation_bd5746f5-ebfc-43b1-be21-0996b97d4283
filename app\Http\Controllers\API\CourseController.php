<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\EnrollRequest;
use App\Http\Resources\CourseListResource;
use App\Http\Resources\SingleCourseResource;
use App\Models\Course;
use App\Models\EnrollForm;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    /**
     * @OA\Get(
     *      path="/courses",
     *      operationId="getCoursesList",
     *      tags={"Courses"},
     *      summary="Get list of courses",
     *      description="Returns list of courses",
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function index(Request $request)
    {
        $courses = Course::where('availability', 1)->get();

        return CourseListResource::collection($courses);
    }

    /**
     * @OA\Get(
     *      path="/courses/{id}",
     *      operationId="getSingleCourseDetails",
     *      tags={"Courses"},
     *      summary="Get Single Course Details",
     *      description="Return Course Details",
     *      @OA\Parameter(
     *          name="id",
     *          description="Course id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function show(Course $course)
    {
        return new SingleCourseResource($course);
    }

    /**
     * @OA\Post(
     *      path="/enroll",
     *      operationId="enroll_course",
     *      tags={"Courses"},
     *      summary="Course Enrollment",
     *      description="Course Enrollment",
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(mediaType="multipart/form-data",
     *              @OA\Schema(
     *                  required={"name","email","phone","age","address","school", "course_id"},
     *               @OA\Property(
     *                      property="name",
     *                      type="string",
     *                      description="User Name"
     *                  ),
     *               @OA\Property(
     *                      property="email",
     *                      type="string",
     *                      description="User Email"
     *                  ),
     *                  @OA\Property(
     *                      property="phone",
     *                      type="string",
     *                      description="User Phone"
     *                  ),
     *                  @OA\Property(
     *                      property="age",
     *                      type="integer",
     *                      description="User Age"
     *                  ),
     *                  @OA\Property(
     *                      property="address",
     *                      type="string",
     *                      description="User Address"
     *                  ),
     *                  @OA\Property(
     *                      property="school",
     *                      type="string",
     *                      description="User School"
     *                  ),
     *                  @OA\Property(
     *                      property="status",
     *                      type="integer",
     *                      description="User Status"
     *                  ),
     *                 @OA\Property(
     *                      property="prereq",
     *                      type="integer",
     *                      description="Course Prerequisites Status"
     *                  ),
     *                 @OA\Property(
     *                      property="course_id",
     *                      type="integer",
     *                      description="ID of course"
     *                  ),
     *             )
     *         )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function enroll(EnrollRequest $request)
    {
        EnrollForm::create($request->input());

        return response()->json(['message' => 'Request Sent Successfully'], 200);
    }


}
