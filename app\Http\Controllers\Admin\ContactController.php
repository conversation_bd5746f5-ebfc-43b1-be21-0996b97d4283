<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $status = request()->input('status', 'all'); // Default to 'all' if no status is provided
        $contacts = Contact::orderBy('created_at', 'desc');

        if ($search) {
            $contacts = $contacts->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('phone', 'like', '%' . $search . '%')
                    ->orWhere('subject', 'like', '%' . $search . '%')
                    ->orWhere('status', 'like', '%' . $search . '%');
            });
        }

        // Filter by status if it's not 'all'
        if ($status !== 'all') {
            $contacts = $contacts->where('status', $status);
        }

        confirmDelete('Delete Message!', "Are you sure you want to delete this message?");

        return view('contacts.index', [
            'contacts' => $contacts->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'status' => $status, // Pass the status to the view
        ]);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact)
    {
        return view('contacts.show', compact('contact'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Contact $contact)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Contact $contact)
    {
        $contact->status = $request->status;
        $contact->save();
        return redirect()->back()->with('success', 'Status updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact)
    {
        $contact->delete();
        return redirect()->route('contacts.index')->with('delete', 'Message Deleted Successfully');
    }
}
