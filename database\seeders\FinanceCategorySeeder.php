<?php

namespace Database\Seeders;

use App\Models\FinanceCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FinanceCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        FinanceCategory::create([
            'id' => 1,
            'name' => 'TESQA core operations',
            'type' => 'both',
        ]);
        FinanceCategory::create([
            'id' => 2,
            'name' => 'Salaries',
            'type' => 'cash_out',
        ]);
    }
}
