<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CourseRequest;
use App\Http\Requests\Admin\UpdateCourseRequest;
use App\Models\Course;
use App\Models\Track;
use App\Models\Level;
use App\Models\Session;
use App\Models\SessionMaterial;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $courses = Course::orderBy('created_at', 'desc');

        if ($search) {
            $courses = $courses
                ->whereAny(['name', 'comm_name'], 'like', '%' . request()->get('search', '') . '%');
        }

        confirmDelete('Delete Course!', "Are you sure you want to delete this course?");

        return view('courses.index', [
            'courses' => $courses->paginate(24)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $tracks = Track::all();
        $courses = Course::pluck('name', 'id');
        return view('courses.create', compact('tracks', 'courses'));
    }

    /**
     * Store a newly created resource in storage.
     */

    public function store(CourseRequest $request)
    {
        if ($request->hasFile('image_path')) {
            $ImageName = time() . '.' . $request->image_path->extension();
            $request->image_path->move(('images/courses'), $ImageName);
        }

        $prereq = $request->input('prereq');
        if ($prereq) {
            $prereq = json_encode($prereq);
        }

        Course::create($request->except('image_path', '_token', 'prereq') +
            ['image_path' => $ImageName, 'prereq' => $prereq]);
        return redirect()->route('courses.index')->with('add', 'Course ' . $request->name . '  Added Successfully');
    }



    /**
     * Display the specified resource.
     */
    public function show(Course $course)
    {
        $levels = Level::where('course_id', $course->id)->get();
        $track = Track::find($course->track_id)->name;
        $courses = Course::pluck('name', 'id');
        $allMaterials = collect();

        foreach ($levels as $level) {
            $level->sessions = Session::where('level_id', $level->id)->get();
            $sessionIds = $level->sessions->pluck('id')->toArray();
            $materials = SessionMaterial::whereIn('session_id', $sessionIds)->get();
            $allMaterials = $allMaterials->merge($materials);
        }
        
        return view('courses.show', compact('track', 'course', 'levels', 'allMaterials', 'courses'));
    }



    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Course $course)
    {
        $tracks = Track::all();
        $courses = Course::pluck('name', 'id');
        return view('courses.edit', compact('course', 'tracks', 'courses'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCourseRequest $request, Course $course)
    {
        $course->update($request->except('image_path', '_token', '_method'));
        if ($request->hasFile('image_path')) {
            $ImageName = time() . '.' . $request->image_path->extension();
            $request->image_path->move(('images/courses'), $ImageName);
            $course->update(['image_path' => $ImageName]);
        }
        return redirect()->route('courses.index')->with('update', 'Course ' . $course->name . ' Updated Successfully');
    }

    public function material_destroy($sessionMaterial)
    {
        $sessionMaterial = SessionMaterial::where('id', $sessionMaterial)->delete();
        return redirect()->back()->with('delete', 'Material Deleted Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */

    public function destroy(Course $course)
    {
        $course->delete();
        return redirect()->route('courses.index')->with('delete', 'Course Deleted Successfully');
    }

}
