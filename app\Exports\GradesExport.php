<?php

namespace App\Exports;

use App\Models\Enrollment;
use Carbon\Carbon;
use DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class GradesExport implements FromCollection, WithHeadings, WithTitle
{
    protected $startOfMonth;
    protected $endOfMonth;
    protected $studentNameFilter;
    protected $courseCycleFilter;

    public function __construct($startOfMonth, $endOfMonth, $studentNameFilter, $courseCycleFilter)
    {
        $this->startOfMonth = $startOfMonth;
        $this->endOfMonth = $endOfMonth;
        $this->studentNameFilter = $studentNameFilter;
        $this->courseCycleFilter = $courseCycleFilter;
    }
    public function collection()
    {
        $lastSessions = DB::table('session_cycles as sc')
            ->select('sc.course_cycle_id', DB::raw('MAX(sc.date) as last_session_date'))
            ->groupBy('sc.course_cycle_id');

        $query = Enrollment::joinSub($lastSessions, 'last_sessions', function ($join) {
            $join->on('enrollments.course_cycle_id', '=', 'last_sessions.course_cycle_id');
        })
            ->join('session_cycles', function ($join) {
                $join->on('session_cycles.course_cycle_id', '=', 'last_sessions.course_cycle_id')
                    ->on('session_cycles.date', '=', 'last_sessions.last_session_date');
            })
            ->join('users', 'enrollments.user_id', '=', 'users.id') 
            ->join('course_cycles', 'enrollments.course_cycle_id', '=', 'course_cycles.id') 
            ->whereBetween('last_sessions.last_session_date', [$this->startOfMonth, $this->endOfMonth]);
        
        if ($this->studentNameFilter) {
            $query->where('users.name', 'like', '%' . $this->studentNameFilter . '%');
        }

        if ($this->courseCycleFilter) {
            $query->where('course_cycles.name', 'like', '%' . $this->courseCycleFilter . '%');
        }

        return $query->select(
            'enrollments.id as enrollment_id',
            'users.name as student_name',
            'course_cycles.name as course_name',
            'last_sessions.last_session_date',
            'enrollments.grade'
        )
            ->get()
            ->map(function ($grade, $index) {
                return [
                    'index' => $index + 1,
                    'student' => $grade->student_name,
                    'course' => $grade->course_name,
                    'graduation_date' => $grade->last_session_date,
                    'grade' => $grade->grade,
                ];
            });
    }


    public function headings(): array
    {
        return [
            '#',
            'Student',
            'Course',
            'Graduation Date',
            'Grade',
        ];
    }

    public function title(): string
    {
        $date = Carbon::parse($this->startOfMonth);
        return 'Students-Grades ' . $date->format('M Y');
    }
}
