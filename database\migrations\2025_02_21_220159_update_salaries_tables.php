<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename existing staff_salaries table to admin_salaries
        Schema::rename('staff_salaries', 'admin_salaries');

        // Create a new staff_salaries table
        Schema::create('staff_salaries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('staff_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->date('month')->nullable();
            $table->decimal('salary')->nullable()->default(0);
            $table->decimal('bonus')->nullable()->default(0);

            $table->timestamps();
        });

        // Drop bonus column from staff table
        Schema::table('staff', function (Blueprint $table) {
            $table->dropColumn('bonus');
        });

        // Drop bonus column from admins table
        Schema::table('admins', function (Blueprint $table) {
            $table->dropColumn('bonus');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore bonus column in staff table
        Schema::table('staff', function (Blueprint $table) {
            $table->decimal('bonus')->nullable()->default(0);
        });

        // Restore bonus column in admins table
        Schema::table('admins', function (Blueprint $table) {
            $table->decimal('bonus')->nullable()->default(0);
        });

        // Drop the new staff_salaries table
        Schema::dropIfExists('staff_salaries');

        // Rename admin_salaries back to staff_salaries
        Schema::rename('admin_salaries', 'staff_salaries');
    }
};
