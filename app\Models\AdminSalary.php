<?php

namespace App\Models;

use App\Services\BranchManager;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminSalary extends Model
{
    use HasFactory;
    public function scopeForCurrentBranch($query)
    {
        $branchManager = app(BranchManager::class);
    
        if ($branchManager->isBranchContextSet()) {
            $branchId = $branchManager->getBranchId();
    
            $query->whereHas('admin.branches', function ($q) use ($branchId) {
                $q->where('branches.id', $branchId);
            });
        }
    
        return $query;
    }
    
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
}
