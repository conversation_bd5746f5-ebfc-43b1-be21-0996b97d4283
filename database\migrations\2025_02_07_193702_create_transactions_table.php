<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->double('balance_before', 15, 8);
            $table->double('balance_after',  15,  8);
            $table->enum('reference_table', ['cash_ins', 'cash_outs']);
            $table->unsignedBigInteger('reference_id');
            $table->timestamps();

            $table->index(['reference_table', 'reference_id']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
