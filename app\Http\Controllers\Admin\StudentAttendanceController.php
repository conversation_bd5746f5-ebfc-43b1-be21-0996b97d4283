<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Models\StudentAttendance;
use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Models\SessionCycle;
use App\Models\CourseCycle;

class StudentAttendanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $startOfMonth = Carbon::now()->startOfMonth()->toDateString();
        $today = Carbon::today();
        $courseCyclesInProgress = CourseCycle::whereIn('course_status', ['in_progress', 'completed'])
        ->pluck('id');    

        $todaySessionCycles = SessionCycle::whereIn('course_cycle_id', $courseCyclesInProgress)
        ->whereBetween('date', [$startOfMonth, $today])
            // ->whereDate('date', $today)
            ->pluck('id');
        $unsubmittedSessions = SessionCycle::with(['courseCycle', 'instructor_attendances'])
            ->whereIn('id', $todaySessionCycles)
            ->where('students_attendance', 0);
        if ($search) {
            $unsubmittedSessions = $unsubmittedSessions
                ->whereAny(['session_cycle_id'], 'like', '%' . request()->get('search', '') . '%');
        }
        return view('course_cycles.attendance', [
            'unsubmittedSessions' => $unsubmittedSessions->paginate(20)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(StudentAttendance $studentAttendance)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StudentAttendance $studentAttendance)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StudentAttendance $studentAttendance)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StudentAttendance $studentAttendance)
    {
        //
    }
}
