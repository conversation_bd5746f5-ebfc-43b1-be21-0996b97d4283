<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'password' => $this->password,
            'phone' => $this->phone,
            'image' => asset('https://admin.mtechsquare.com/images/profiles/' . $this->profile_img),
            'date_of_birth' => $this->date_of_birth,
            'city' => $this->city,
            'address' => $this->address,
            'gender' => $this->gender,
            'date_of_join' => $this->date_of_join,
            'school' => $this->school,
            'mother_phone' => $this->mother_phone,
            'father_phone' => $this->father_phone,
            'mother_job' => $this->mother_job,
            'father_job' => $this->father_job,
            'status' => $this->status,
        ];
    }
}
