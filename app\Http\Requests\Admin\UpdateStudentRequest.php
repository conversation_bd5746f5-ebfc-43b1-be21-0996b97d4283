<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Carbon\Carbon;
class UpdateStudentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'min:8'],
            'password' => ['nullable', 'string','min:8'],
            'email' => ['required', 'email', 'unique:users,email,' . $this->student->id],
            'phone' => ['required', 'digits:11', 'regex:/^(010|011|012)\d{8}$/', 'unique:users,phone,' . $this->student->id],
            'date_of_birth' => ['required', 'date', 'before_or_equal:' . Carbon::now()->subYears(5)->format('Y-m-d')],
            'city' => ['required'],
            'address' => ['required'],
            'gender' => ['required'],
            'date_of_join' => ['required'],
            'school' => ['required'],
            'mother_phone' => ['required', 'digits:11', 'regex:/^(010|011|012)\d{8}$/'],
            'father_phone' => ['required', 'digits:11', 'regex:/^(010|011|012)\d{8}$/'],
            'mother_job' => ['required'],
            'father_job' => ['required'],
            'status' => ['required'],
        ];
    }
}
