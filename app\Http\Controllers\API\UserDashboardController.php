<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\StudentFeedbackRequest;
use App\Http\Resources\CourseGroupResource;
use App\Http\Resources\UserResource;
use App\Models\CourseCycle;
use App\Models\Enrollment;
use App\Models\SessionCycle;
use App\Models\StudentAttendance;
use App\Models\StudentFeedback;
use Illuminate\Http\Request;

class UserDashboardController extends Controller
{
    /**
     * @OA\Get(
     *      path="/student_profile",
     *      operationId="getStudentInfo",
     *      tags={"Student"},
     *      summary="Get student info",
     *      description="Returns student info",
     *      security={ {"sanctum": {} }},
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function profile()
    {
        return (new UserResource(auth('student')->user()));
    }

    /**
     * @OA\Post(
     *      path="/student_image",
     *      operationId="update_student_image",
     *      tags={"Student"},
     *      summary="Student Profile Image",
     *      description="Updating Student Profile Image",
     *      security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="profile_img",
     *                     type="string",
     *                     format="binary",
     *                     description="Profile image file"
     *                 )
     *             )
     *         )
     *     ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *              mediaType="application/json",
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not found"
     *      ),
     * )
     */

    public function updateProfileImage(Request $request)
    {
        $request->validate([
            'profile_img' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($request->hasFile('profile_img')) {
            $imageName = time() . '.' . $request->profile_img->extension();
            $request->profile_img->move(('images/profiles'), $imageName);
        }
        auth('student')->user()->update(['profile_img' => $imageName]);
        return response()->json([
            'success' => true,
            'message' => 'Profile image updated successfully!',
        ]);
    }

    /**
     * @OA\Get(
     *      path="/student_courses",
     *      operationId="getStudentCourseGroups",
     *      tags={"Student - Courses"},
     *      summary="Get Student Course Groups",
     *      description="Returns Student Course Groups",
     *      security={ {"sanctum": {} }},
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function courses()
    {
        $enrollments = Enrollment::where('user_id', auth('student')->user()->id)->get();
        $coursesIds = $enrollments->pluck('course_cycle_id')->toArray();
        $course_groups = CourseCycle::whereIn('id', $coursesIds)->orderby('start_date','desc')->get();
        return CourseGroupResource::collection($course_groups);
    }

    /**
     * @OA\Get(
     *      path="/student_comment/{id}",
     *      operationId="getStudentComment",
     *      tags={"Student - Courses"},
     *      summary="Get Student Comment",
     *      description="Get Student Comment of session",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function student_comment($id)
    {
        $comment = StudentAttendance::where('session_cycle_id', $id)
            ->where('user_id', auth('student')->user()->id)->get('comment');
        return response()->json(['comment' => $comment]);
    }

    /**
     * @OA\Get(
     *      path="/student_attendance/{id}",
     *      operationId="getStudentAttendance",
     *      tags={"Student - Courses"},
     *      summary="Get Student Attendance",
     *      description="Get Student Attendance of session",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function student_attendance($id)
    {
        $attendance = StudentAttendance::where('session_cycle_id', $id)
            ->where('user_id', auth('student')->user()->id)->get('attendance');
        return response()->json(['attendance' => $attendance]);
    }

    /**
     * @OA\Get(
     *      path="/student_grade/{id}",
     *      operationId="getStudentGrade",
     *      tags={"Student - Courses"},
     *      summary="Get Student Grade",
     *      description="Get Student Course Grade",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Course id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function student_grade($id)
    {
        $grade = Enrollment::where('course_cycle_id', $id)
            ->where('user_id', auth('student')->user()->id)->get('grade');
        return response()->json(['grade' => $grade]);
    }

    /**
     * @OA\Get(
     *      path="/instructor_material/{id}",
     *      operationId="getInstructorMaterial",
     *      tags={"Student - Courses"},
     *      summary="Get Material Link",
     *      description="Get Session Material Link",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function instructor_material($sessionId)
    {
        $material = SessionCycle::where('id', $sessionId)->get('material_link');
        return response()->json(['material' => $material]);
    }

    /**
     * @OA\Post(
     *      path="/student_feedback/{id}",
     *      operationId="student_feedback",
     *      tags={"Student - Courses"},
     *      summary="Sending Session Feedback for Admin",
     *      description="Sending Session Feedback for Admin",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  required={"feedback"},
     *                  @OA\Property(
     *                      property="feedback",
     *                      type="string",
     *                      description="Feedback from Students"
     *                  ),
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     * )
     */
    public function student_feedback(StudentFeedbackRequest $request, $sessionId)
    {
        $feedback = $request->input('feedback');
        $student = auth('student')->user();
        $session = StudentAttendance::where('user_id', $student->id)
            ->where('session_cycle_id', $sessionId)
            ->first();
        if (!$session) {
            return response()->json(['message' => 'Session not found'], 404);
        }
        
        StudentFeedback::create([
            'user_id' => $student->id,
            'session_cycle_id' => $sessionId,
            'feedback' => $feedback,
        ]);
        return response()->json(['message' => 'Session Feedback Sent Successfully'], 200);
    }

}
