<?php

namespace App\Models;

use App\Services\BranchManager;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transaction extends Model
{
    public function formattedType(): Attribute
    {
        return Attribute::get(function () {
            $types = [
                'bank_account' => 'Bank Account',
                'vodafone_cash' => 'Vodafone Cash',
                'instapay' => 'Instapay',
                'cash' => 'Cash',
            ];

            return $types[$this->type] ?? $this->type;
        });
    }


    public function scopeForCurrentBranch($query)
    {
        $branchManager = app(BranchManager::class);

        if ($branchManager->isBranchContextSet()) {
            $branchId = $branchManager->getBranchId();

            $query->where(function ($q) use ($branchId) {
                $q->where(function ($q2) use ($branchId) {
                    $q2->where('reference_table', 'cash_ins')
                        ->whereHasMorph('reference', [\App\Models\CashIn::class], function ($q3) use ($branchId) {
                            $q3->where('branch_id', $branchId);
                        });
                })->orWhere(function ($q2) use ($branchId) {
                    $q2->where('reference_table', 'cash_outs')
                        ->whereHasMorph('reference', [\App\Models\CashOut::class], function ($q3) use ($branchId) {
                            $q3->where('branch_id', $branchId);
                        });
                });
            });
        }

        return $query;
    }


    protected static function booted(): void
    {
        static::addGlobalScope('branch_filter', function ($query) {
            $query->forCurrentBranch();
        });
    }

    public function balance(): BelongsTo
    {
        return $this->belongsTo(Balance::class, 'type', 'type');
    }

    public function reference()
    {
        return $this->morphTo(null, 'reference_table', 'reference_id');
    }

    public function getTransactionTypeAttribute()
    {
        return $this->reference_table === 'cash_ins' ? 'Cash In' : 'Cash Out';
    }
}
