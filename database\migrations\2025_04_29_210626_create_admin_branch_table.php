<?php

// database/migrations/xxxx_xx_xx_create_admin_branch_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('admin_branch', function (Blueprint $table) {
            $table->id();
            $table->foreignId('admin_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->unique(['admin_id', 'branch_id']);
        });
    }

    public function down(): void {
        Schema::dropIfExists('admin_branch');
    }
};

