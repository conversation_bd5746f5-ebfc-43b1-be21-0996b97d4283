<?php

namespace App\Models;

use App\Scopes\BranchScope;
use App\Services\BranchManager;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Carbon\Carbon;


class Admin extends Authenticatable
{
    use HasFactory;
    // protected static function booted(): void
    // {
    //     // Apply the global scope
    //     static::addGlobalScope(new BranchScope());
    // }

    public function scopeForCurrentBranch($query)
    {
        $branchManager = app(BranchManager::class);

        if ($branchManager->isBranchContextSet()) {
            $branchId = $branchManager->getBranchId();

            // Filter admins who belong to the current branch
            $query->whereHas('branches', function ($q) use ($branchId) {
                $q->where('branches.id', $branchId);
            });
        }

        return $query;
    }

    protected $guard = 'admin';

    protected $fillable = ['name', 'email', 'password', 'remember_token'];

    protected $hidden = ['password', 'remember_token'];
    protected $casts = [
        'date_of_birth' => 'date',
    ];
    public function getAgeAttribute()
    {
        return Carbon::parse($this->date_of_birth)->age;
    }

    public function admin_salaries()
    {
        return $this->hasMany(AdminSalary::class);
    }

    // app/Models/Admin.php
    public function branches()
    {
        return $this->belongsToMany(Branch::class, 'admin_branch');
    }
}
