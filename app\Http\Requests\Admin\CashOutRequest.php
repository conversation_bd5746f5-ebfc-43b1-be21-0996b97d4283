<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\UniqueCaseInsensitive;

class CashOutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'finance_category_id' => 'required|exists:finance_categories,id',
            'finance_sub_category_id' => 'nullable|exists:finance_sub_categories,id',
            'date' => 'required|date|before_or_equal:today',
            'amount' => 'required|numeric|min:0',
            'note' => 'required|string',
        ];
    }
}
