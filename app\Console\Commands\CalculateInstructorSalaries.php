<?php

namespace App\Console\Commands;

use App\Models\Admin;
use App\Models\AdminSalary;
use App\Models\CashOut;
use App\Models\Instructor;
use App\Models\InstructorAttendance;
use App\Models\InstructorSalary;
use App\Models\SessionCycle;
use App\Models\CourseCycle;
use App\Models\Enrollment;
use App\Models\Staff;
use App\Models\StaffSalary;
use Carbon\Carbon;
use DB;
use Illuminate\Console\Command;

class CalculateInstructorSalaries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:salaries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate salaries for instructors, staff, and admins for the previous month';

    /**
     * Execute the console command.
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $previousMonthStart = Carbon::now()->subMonth()->startOfMonth();
        $previousMonthEnd = Carbon::now()->subMonth()->endOfMonth();

        $attendances = InstructorAttendance::whereBetween('created_at', [
            $previousMonthStart,
            $previousMonthEnd
        ])->get();

        $workHoursByInstructor = $attendances->groupBy('instructor_id');

        foreach ($workHoursByInstructor as $instructorId => $attendances) {
            $instructor = Instructor::find($instructorId);
            $fixedSalary = $instructor->fixed_salary;
            $variableSalary = $instructor->variable_salary;

            $totalWorkHours = $attendances->count() * 2;
            $totalFixedSalary = 0;
            $totalVariableSalary = 0;

            foreach ($attendances as $attendance) {
                $sessionCycle = SessionCycle::find($attendance->session_cycle_id);
                $courseCycle = CourseCycle::find($sessionCycle->course_cycle_id);
                $numberOfStudents = Enrollment::where('course_cycle_id', $courseCycle->id)
                    ->where('status', 1)->count();
                if ($numberOfStudents < 6) {
                    $numberOfStudents = 6;
                }
                $totalFixedSalary += 2 * $fixedSalary;
                $totalVariableSalary += 2 * $variableSalary * (($numberOfStudents - 1) / 10);
            }
            $totalSalary = $totalFixedSalary + $totalVariableSalary;
            InstructorSalary::updateOrCreate(
                [
                    'instructor_id' => $instructorId,
                    'month' => $previousMonthStart,
                ],
                [
                    'salary' => $totalSalary,
                    'work_hours' => $totalWorkHours,
                ]
            );
        }


                // Calculate staff salaries
                $staffMembers = Staff::all();
                foreach ($staffMembers as $staff) {
                    StaffSalary::updateOrCreate(
                        [
                            'staff_id' => $staff->id,
                            'month' => $previousMonthStart,
                        ],
                        [
                            'salary' => $staff->salary,
                        ]
                    );
                }

        // Calculate admin salaries
        $admins = Admin::where('role', '!=', 'super_admin')->get();
        foreach ($admins as $admin) {
            AdminSalary::updateOrCreate(
                [
                    'admin_id' => $admin->id,
                    'month' => $previousMonthStart,
                ],
                [
                    'salary' => $admin->salary,
                ]
            );
        }

        $this->info('Instructor, staff, and admin salaries calculated for the previous month.');
    }
}
