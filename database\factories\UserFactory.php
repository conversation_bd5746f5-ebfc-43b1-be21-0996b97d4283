<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'remember_token' => Str::random(10),
            'date_of_birth' => fake()->date(),
            'city' => fake()->city(),
            'address' => fake()->address(),
            'date_of_join' => fake()-> date(),
            'school' => fake()->company(),
            'phone' => fake()->phoneNumber(),
            'mother_phone' => fake()->phoneNumber(),
            'father_phone' => fake()->phoneNumber(),
            'mother_job' => fake()->jobTitle(),
            'father_job' => fake()->jobTitle(),
            'status' => fake()->numberBetween(0,1),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return $this
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
