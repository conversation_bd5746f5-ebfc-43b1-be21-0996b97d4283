<?php


namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CashIn;
use App\Models\CashOut;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $year = request()->input('year');
        $startDate = request()->input('start_date');
        $endDate = request()->input('end_date');

        // Start with the base query
        $payments = Payment::forCurrentBranch()->where('amount', '>', 0)->orderBy('created_at', 'desc');

        // Apply search filter
        if ($search) {
            $payments = $payments->where(function ($query) use ($search) {
                $query->where('amount', 'like', '%' . $search . '%')
                    ->orWhere('date', 'like', '%' . $search . '%');
            });
        }

        // Apply year filter
        if ($year) {
            $payments = $payments->whereYear('date', $year);
        }

        // Apply start date filter
        if ($startDate) {
            $payments = $payments->whereDate('date', '>=', $startDate);
        }

        // Apply end date filter
        if ($endDate) {
            $payments = $payments->whereDate('date', '<=', $endDate);
        }

        confirmDelete('Delete Payment!', "Are you sure you want to delete this payment?");

        return view('payments.index', [
            'payments' => $payments->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'year' => $year,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    public function getRefunds()
    {
        $search = request()->input('search');
        $year = request()->input('year');
        $startDate = request()->input('start_date');
        $endDate = request()->input('end_date');

        // Start with the base query for refunds
        $refunds = Payment::forCurrentBranch()->where('amount', '<', 0)->orderBy('created_at', 'desc');

        // Apply search filter
        if ($search) {
            $refunds = $refunds->where(function ($query) use ($search) {
                $query->where('amount', 'like', '%' . $search . '%')
                    ->orWhere('date', 'like', '%' . $search . '%');
            });
        }

        // Apply year filter
        if ($year) {
            $refunds = $refunds->whereYear('date', $year);
        }

        // Apply start date filter
        if ($startDate) {
            $refunds = $refunds->whereDate('date', '>=', $startDate);
        }

        // Apply end date filter
        if ($endDate) {
            $refunds = $refunds->whereDate('date', '<=', $endDate);
        }

        confirmDelete('Delete Refund!', "Are you sure you want to delete this refund?");

        return view('payments.refunds', [
            'refunds' => $refunds->paginate(15),
            'search' => $search,
            'year' => $year,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'enrollment_id' => 'required|exists:enrollments,id',
            'amount' => 'required|numeric',
            'type' => 'required|in:0,1',
            'payment_way' => 'required|in:cash,instapay,vodafone_cash'
        ]);

        $payment = Payment::create([
            'enrollment_id' => $request->enrollment_id,
            'date' => Carbon::now(),
            'amount' => $request->amount,
            'type' => $request->type,
            'payment_way' => $request->payment_way
        ]);

        // Get enrollment and payment details
        $enrollment = $payment->enrollment;
        $amount = $payment->amount;
        $isNegative = $amount < 0;
        $absAmount = abs($amount);

        // Recalculate payment amounts based on all payments
        $enrollment->recalculatePaymentAmounts();

        if ($payment->type == 0) { // Course payment
            // Create cash record based on whether amount is positive or negative
            if ($isNegative) {
                // Create cash out record for negative amount
                CashOut::create([
                    'finance_category_id' => 15,
                    'date' => Carbon::now(),
                    'amount' => $absAmount,
                    'note' => "student's course payment refund for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                    'payment_way' => $payment->payment_way,
                ]);
            } else {
                // Create cash in record for positive amount
                CashIn::create([
                    'finance_category_id' => 1,
                    'finance_sub_category_id' => 1,
                    'date' => Carbon::now(),
                    'amount' => $amount,
                    'note' => "student's course payment for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                    'payment_way' => $payment->payment_way,
                ]);
            }
        } else { // Material payment
            // Note: We already recalculated the payment amounts above

            // Create cash record based on whether amount is positive or negative
            if ($isNegative) {
                // Create cash out record for negative amount
                CashOut::create([
                    'finance_category_id' => 15,
                    'date' => Carbon::now(),
                    'amount' => $absAmount,
                    'note' => "student's material payment refund for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                    'payment_way' => $payment->payment_way,
                ]);
            } else {
                // Create cash in record for positive amount
                CashIn::create([
                    'finance_category_id' => 1,
                    'finance_sub_category_id' => 1,
                    'date' => Carbon::now(),
                    'amount' => $amount,
                    'note' => "student's material payment for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                    'payment_way' => $payment->payment_way,
                ]);
            }
        }

        $enrollment->save();

        return response()->json([
            'success' => true,
            'message' => 'Payment added successfully',
            'payment' => $payment,
            'enrollment' => $enrollment
        ]);
    }


    /**
     * Display the specified resource.
     */
    public function show(Payment $payment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Payment $payment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $payment = Payment::findOrFail($id);
        $request->validate([
            'amount' => 'required|numeric',
            'payment_way' => 'required|string'
        ]);

        $oldAmount = $payment->amount;
        $oldPaymentWay = $payment->payment_way;
        $amountDifference = $request->amount - $oldAmount;

        // Determine if the payment was/is negative
        $wasNegative = $oldAmount < 0;
        $isNegative = $request->amount < 0;

        // Find related cash records
        $relatedCashIn = $payment->findRelatedCashIn();
        $relatedCashOut = $payment->findRelatedCashOut();

        // Update the payment
        $payment->update([
            'amount' => $request->amount,
            'payment_way' => $request->payment_way,
        ]);

        // Get enrollment and recalculate payment amounts
        $enrollment = $payment->enrollment;
        $enrollment->recalculatePaymentAmounts();

        if ($payment->type == 0) { // Course payment

            // Handle cash records based on whether the payment was/is negative
            if ($wasNegative && $isNegative) {
                // Both old and new amounts are negative, update the CashOut record
                if ($relatedCashOut) {
                    $relatedCashOut->update([
                        'amount' => abs($request->amount),
                        'payment_way' => $request->payment_way,
                    ]);
                    Log::info('Updated CashOut record for negative payment', [
                        'payment_id' => $payment->id,
                        'old_amount' => $oldAmount,
                        'new_amount' => $request->amount,
                        'cash_out_id' => $relatedCashOut->id
                    ]);
                } else {
                    // Create a new CashOut record if we couldn't find the related one
                    CashOut::create([
                        'finance_category_id' => 15,
                        'date' => Carbon::now(),
                        'amount' => abs($request->amount),
                        'note' => "student's course payment refund for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                        'payment_way' => $request->payment_way,
                    ]);
                    Log::info('Created new CashOut record for negative payment', [
                        'payment_id' => $payment->id,
                        'amount' => $request->amount
                    ]);
                }
            } else if (!$wasNegative && !$isNegative) {
                // Both old and new amounts are positive, update the CashIn record
                if ($relatedCashIn) {
                    $relatedCashIn->update([
                        'amount' => $request->amount,
                        'payment_way' => $request->payment_way,
                    ]);
                    Log::info('Updated CashIn record for positive payment', [
                        'payment_id' => $payment->id,
                        'old_amount' => $oldAmount,
                        'new_amount' => $request->amount,
                        'cash_in_id' => $relatedCashIn->id
                    ]);
                } else {
                    // Create a new CashIn record if we couldn't find the related one
                    CashIn::create([
                        'finance_category_id' => 1,
                        'finance_sub_category_id' => 1,
                        'date' => Carbon::now(),
                        'amount' => $request->amount,
                        'note' => "student's course payment for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                        'payment_way' => $request->payment_way,
                    ]);
                    Log::info('Created new CashIn record for positive payment', [
                        'payment_id' => $payment->id,
                        'amount' => $request->amount
                    ]);
                }
            } else if (!$wasNegative && $isNegative) {
                // Payment changed from positive to negative
                // Delete the old CashIn record
                if ($relatedCashIn) {
                    $relatedCashIn->delete();
                    Log::info('Deleted CashIn record for payment that changed from positive to negative', [
                        'payment_id' => $payment->id,
                        'old_amount' => $oldAmount,
                        'new_amount' => $request->amount,
                        'cash_in_id' => $relatedCashIn->id
                    ]);
                }

                // Create a new CashOut record
                CashOut::create([
                    'finance_category_id' => 15,
                    'date' => Carbon::now(),
                    'amount' => abs($request->amount),
                    'note' => "student's course payment refund for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                    'payment_way' => $request->payment_way,
                ]);
                Log::info('Created new CashOut record for payment that changed from positive to negative', [
                    'payment_id' => $payment->id,
                    'amount' => $request->amount
                ]);
            } else if ($wasNegative && !$isNegative) {
                // Payment changed from negative to positive
                // Delete the old CashOut record
                if ($relatedCashOut) {
                    $relatedCashOut->delete();
                    Log::info('Deleted CashOut record for payment that changed from negative to positive', [
                        'payment_id' => $payment->id,
                        'old_amount' => $oldAmount,
                        'new_amount' => $request->amount,
                        'cash_out_id' => $relatedCashOut->id
                    ]);
                }

                // Create a new CashIn record
                CashIn::create([
                    'finance_category_id' => 1,
                    'finance_sub_category_id' => 1,
                    'date' => Carbon::now(),
                    'amount' => $request->amount,
                    'note' => "student's course payment for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                    'payment_way' => $request->payment_way,
                ]);
                Log::info('Created new CashIn record for payment that changed from negative to positive', [
                    'payment_id' => $payment->id,
                    'amount' => $request->amount
                ]);
            }
        } else { // Material payment
            // Note: We already recalculated the payment amounts above

            // Handle cash records based on whether the payment was/is negative
            if ($wasNegative && $isNegative) {
                // Both old and new amounts are negative, update the CashOut record
                if ($relatedCashOut) {
                    $relatedCashOut->update([
                        'amount' => abs($request->amount),
                        'payment_way' => $request->payment_way,
                    ]);
                    Log::info('Updated CashOut record for negative payment', [
                        'payment_id' => $payment->id,
                        'old_amount' => $oldAmount,
                        'new_amount' => $request->amount,
                        'cash_out_id' => $relatedCashOut->id
                    ]);
                } else {
                    // Create a new CashOut record if we couldn't find the related one
                    CashOut::create([
                        'finance_category_id' => 15,
                        'date' => Carbon::now(),
                        'amount' => abs($request->amount),
                        'note' => "student's material payment refund for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                        'payment_way' => $request->payment_way,
                    ]);
                    Log::info('Created new CashOut record for negative payment', [
                        'payment_id' => $payment->id,
                        'amount' => $request->amount
                    ]);
                }
            } else if (!$wasNegative && !$isNegative) {
                // Both old and new amounts are positive, update the CashIn record
                if ($relatedCashIn) {
                    $relatedCashIn->update([
                        'amount' => $request->amount,
                        'payment_way' => $request->payment_way,
                    ]);
                    Log::info('Updated CashIn record for positive payment', [
                        'payment_id' => $payment->id,
                        'old_amount' => $oldAmount,
                        'new_amount' => $request->amount,
                        'cash_in_id' => $relatedCashIn->id
                    ]);
                } else {
                    // Create a new CashIn record if we couldn't find the related one
                    CashIn::create([
                        'finance_category_id' => 1,
                        'finance_sub_category_id' => 1,
                        'date' => Carbon::now(),
                        'amount' => $request->amount,
                        'note' => "student's material payment for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                        'payment_way' => $request->payment_way,
                    ]);
                    Log::info('Created new CashIn record for positive payment', [
                        'payment_id' => $payment->id,
                        'amount' => $request->amount
                    ]);
                }
            } else if (!$wasNegative && $isNegative) {
                // Payment changed from positive to negative
                // Delete the old CashIn record
                if ($relatedCashIn) {
                    $relatedCashIn->delete();
                    Log::info('Deleted CashIn record for payment that changed from positive to negative', [
                        'payment_id' => $payment->id,
                        'old_amount' => $oldAmount,
                        'new_amount' => $request->amount,
                        'cash_in_id' => $relatedCashIn->id
                    ]);
                }

                // Create a new CashOut record
                CashOut::create([
                    'finance_category_id' => 15,
                    'date' => Carbon::now(),
                    'amount' => abs($request->amount),
                    'note' => "student's material payment refund for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                    'payment_way' => $request->payment_way,
                ]);
                Log::info('Created new CashOut record for payment that changed from positive to negative', [
                    'payment_id' => $payment->id,
                    'amount' => $request->amount
                ]);
            } else if ($wasNegative && !$isNegative) {
                // Payment changed from negative to positive
                // Delete the old CashOut record
                if ($relatedCashOut) {
                    $relatedCashOut->delete();
                    Log::info('Deleted CashOut record for payment that changed from negative to positive', [
                        'payment_id' => $payment->id,
                        'old_amount' => $oldAmount,
                        'new_amount' => $request->amount,
                        'cash_out_id' => $relatedCashOut->id
                    ]);
                }

                // Create a new CashIn record
                CashIn::create([
                    'finance_category_id' => 1,
                    'finance_sub_category_id' => 1,
                    'date' => Carbon::now(),
                    'amount' => $request->amount,
                    'note' => "student's material payment for {$enrollment->user->name} - {$enrollment->course_cycle->level->course->name}",
                    'payment_way' => $request->payment_way,
                ]);
                Log::info('Created new CashIn record for payment that changed from negative to positive', [
                    'payment_id' => $payment->id,
                    'amount' => $request->amount
                ]);
            }
        }

        $enrollment->save();

        return response()->json([
            'success' => true,
            'message' => 'Payment updated successfully',
            'payment' => $payment,
            'enrollment' => $enrollment
        ]);
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payment $payment)
    {
        $enrollment = $payment->enrollment;

        // Delete the payment first (so it's not included in recalculation)
        $payment->delete();

        Log::info('Payment deleted successfully', [
            'payment_id' => $payment->id,
            'enrollment_id' => $enrollment->id
        ]);

        $enrollment->save();

        // Check if this is an AJAX request
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Payment Deleted Successfully',
                'enrollment' => $enrollment
            ]);
        }

        // If the request came from the enrollment edit page, redirect back there
        if (request()->has('redirect_to') && request()->redirect_to === 'enrollment_edit') {
            // Use the SweetAlert package to show a success message
            alert()->success('Success', 'Payment Deleted Successfully')->autoClose(3000);
            return redirect()->route('enrollments.edit', $enrollment->id);
        }

        // Otherwise, redirect to the payments index
        alert()->success('Success', 'Payment Deleted Successfully')->autoClose(3000);
        return redirect()->route('payments.index');
    }
}
