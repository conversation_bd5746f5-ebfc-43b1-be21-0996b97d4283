<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\SendSessionCommentRequest;
use App\Http\Requests\API\SendStudentAttendanceRequest;
use App\Http\Requests\API\SendStudentComment;
use App\Http\Requests\API\SendStudentGrade;
use App\Http\Requests\API\UploadMaterialRequest;
use App\Http\Resources\CourseGroupResource;
use App\Http\Resources\InstructorResource;
use App\Http\Resources\SessionListResource;
use App\Http\Resources\SessionMaterialResource;
use App\Http\Resources\UserResource;
use App\Models\CourseCycle;
use App\Models\Enrollment;
use App\Models\SessionCycle;
use App\Models\SessionMaterial;
use App\Models\StudentAttendance;
use Carbon\Carbon;
use Illuminate\Http\Request;

class InstructorDashboardController extends Controller
{
    /**
     * @OA\Get(
     *      path="/instructor_profile",
     *      operationId="getInstructorInfo",
     *      tags={"Instructor"},
     *      summary="Get Instructor info",
     *      description="Returns Instructor info",
     *      security={ {"sanctum": {} }},
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function profile()
    {
        return (new InstructorResource(auth('instructor')->user()));
    }

    /**
     * @OA\Post(
     *      path="/instructor_image",
     *      operationId="update_instructor_image",
     *      tags={"Instructor"},
     *      summary="Instructor Profile Image",
     *      description="Updating Instructor Profile Image",
     *      security={{"sanctum":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="profile_img",
     *                     type="string",
     *                     format="binary",
     *                     description="Profile image file"
     *                 )
     *             )
     *         )
     *     ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean",
     *                  example=true
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string",
     *                  example="Profile image updated successfully!"
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not found"
     *      ),
     * )
     */
    public function updateProfileImage(Request $request)
    {
        $request->validate([
            'profile_img' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($request->hasFile('profile_img')) {
            $imageName = time() . '.' . $request->profile_img->extension();
            $request->profile_img->move(('images/instructors'), $imageName);
        }
        auth('instructor')->user()->update(['profile_img' => $imageName]);
        return response()->json([
            'success' => true,
            'message' => 'Profile image updated successfully!',
        ]);
    }

    /**
     * @OA\Get(
     *      path="/instructor_courses",
     *      operationId="getInstructorCourseGroups",
     *      tags={"Instructor - Courses"},
     *      summary="Get Instructor Course Groups",
     *      description="Returns Instructor Course Groups",
     *      security={ {"sanctum": {} }},
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function courses()
    {
        $course_groups = CourseCycle::where('instructor_id', auth('instructor')->user()->id)->orderby('start_date', 'desc')->get();
        return CourseGroupResource::collection($course_groups);
    }

    /**
     * @OA\Get(
     *      path="/enrolled_students/{id}",
     *      operationId="getStudentsListOfCourseGroup",
     *      tags={"Instructor - Courses"},
     *      summary="Get Students List Of Course Group",
     *      description="Returns Students List Of Course Group",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Course Group id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function enrolled_students($id)
    {
        $courseCycle = CourseCycle::findOrFail($id);
        $enrollments = $courseCycle->enrollments()->with('user')->get();
        return UserResource::collection($enrollments->pluck('user')->unique());
    }

    /**
     * @OA\Get(
     *      path="/course_sessions/{id}",
     *      operationId="getSessionsOfCourseGroup",
     *      tags={"Instructor - Courses"},
     *      summary="Get Sessions List Of Course Group",
     *      description="Returns Sessions List Of Course Group",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Course Group id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function course_sessions($id)
    {
        $sessions = SessionCycle::where('course_cycle_id', $id)->get();
        return SessionListResource::collection($sessions);
    }

    /**
     * @OA\Post(
     *      path="/session_comment/{id}",
     *      operationId="session_comment",
     *      tags={"Instructor - Courses"},
     *      summary="Session Feedback",
     *      description="Instructor's Session Feedback",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Course Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(mediaType="multipart/form-data",
     *              @OA\Schema(
     *                  required={"comment"},
     *                  @OA\Property(
     *                      property="comment",
     *                      type="string",
     *                      description="Session Feedback"
     *                  ),
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *              mediaType="application/json",
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     * )
     */
    public function session_comment(SendSessionCommentRequest $request, $id)
    {
        $session = SessionCycle::findOrFail($id);
        if ($session && Carbon::parse($session->date)->isFuture()) {
            return response()->json([
                'message' => 'Comment cannot be submitted before the session date.'
            ], 400); 
        }
        $session->update(['comment' => $request->input('comment')]);
        return response()->json(['message' => 'Comment Sent Successfully'], 200);
    }

    /**
     * @OA\Get(
     *      path="/course_materials/{id}",
     *      operationId="getCourseMaterials",
     *      tags={"Instructor - Courses"},
     *      summary="Get Materials of Course",
     *      description="Returns Course Material",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Course id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     *     )
     */
    public function course_materials($course_cycle)
    {
        $sessions = SessionCycle::where('course_cycle_id', $course_cycle)->with('session')->get();
        $sessionIds = $sessions->pluck('session_id');
        $materials = SessionMaterial::whereIn('session_id', $sessionIds)->with('session', 'session_cycle')->orderBy('session_id')->get();
        $groupedMaterials = $materials->groupBy('session_id');

        $result = $sessions->map(function ($session) use ($groupedMaterials) {
            $sessionId = $session->session_id;

            return [
                'session_id' => $sessionId,
                'session_name' => $session->session->name,
                'session_date' => $session->date,
                'materials' => isset($groupedMaterials[$sessionId])
                    ? SessionMaterialResource::collection($groupedMaterials[$sessionId])
                    : [],
                'material_link' => $session->material_link,
            ];
        });


        return response()->json($result->values());
    }


    /**
     * @OA\Post(
     *      path="/student_comment/{id}",
     *      operationId="student_comment",
     *      tags={"Instructor - Courses"},
     *      summary="Sending Session Comments for Students",
     *      description="Sending session comments for students",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  required={"user_id", "comment"},
     *                  @OA\Property(
     *                      property="user_id",
     *                      type="array",
     *                      @OA\Items(
     *                          type="integer"
     *                      ),
     *                      description="Array of student IDs"
     *                  ),
     *                  @OA\Property(
     *                      property="comment",
     *                      type="string",
     *                      description="Comment for students"
     *                  ),
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     * )
     */
    public function student_comment(SendStudentComment $request, $sessionId)
    {
        $session = SessionCycle::find($sessionId);
        if ($session && Carbon::parse($session->date)->isFuture()) {
            return response()->json([
                'message' => 'Attendance cannot be submitted before the session date.'
            ], 400); 
        }
        
        $studentIds = $request->input('user_id');
        $comment = $request->input('comment');
        foreach ($studentIds as $studentId) {
            $students = StudentAttendance::where('user_id', $studentId)
                ->where('session_cycle_id', $sessionId)
                ->first();

            if ($students) {
                $students->update(['comment' => $comment]);
            }
        }
        return response()->json(['message' => 'Comments Sent Successfully'], 200);
    }


    /**
     * @OA\Post(
     *      path="/upload_material/{id}",
     *      operationId="upload_material",
     *      tags={"Instructor - Courses"},
     *      summary="Material Link Upload",
     *      description="Session's Material Link Upload",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(mediaType="multipart/form-data",
     *              @OA\Schema(
     *                  required={"material_link"},
     *                  @OA\Property(
     *                      property="material_link",
     *                      type="string",
     *                      description="Material Link"
     *                  ),
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *              mediaType="application/json",
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     * )
     */
    public function upload_material(UploadMaterialRequest $request, $sessionId)
    {
        $session = SessionCycle::findOrFail($sessionId);
        $session->update(['material_link' => $request->input('material_link')]);
        return response()->json(['message' => 'Material Link Uploaded Successfully'], 200);
    }

    /**
     * @OA\Delete(
     *      path="/delete_material/{id}",
     *      operationId="delete_material",
     *      tags={"Instructor - Courses"},
     *      summary="Delete Material Link",
     *      description="Delete the Session's Material Link",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *              mediaType="application/json",
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not found"
     *      ),
     * )
     */
    public function delete_material($sessionId)
    {
        $session = SessionCycle::findOrFail($sessionId);
        if (is_null($session->material_link)) {
            return response()->json(['message' => 'No material link found for this session to delete.'], 400);
        }
        $session->update(['material_link' => null]);
        return response()->json(['message' => 'Material Link Deleted Successfully'], 200);
    }



    /**
     * @OA\Post(
     *      path="/students_attendance/{id}",
     *      operationId="students_attendance",
     *      tags={"Instructor - Courses"},
     *      summary="Students Attendance",
     *      description="Sending Students Attendance in the session",
     *      security={{"sanctum":{}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Course Session id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  required={"student_ids"},
     *                  @OA\Property(
     *                      property="student_ids",
     *                      type="array",
     *                      @OA\Items(type="integer"),
     *                      description="Array of Student IDs"
     *                  ),
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *              mediaType="application/json",
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not found"
     *      ),
     * )
     */

    public function students_attendance(SendStudentAttendanceRequest $request, $id)
    {
        $session = SessionCycle::find($id);
        if ($session && Carbon::parse($session->date)->isFuture()) {
            return response()->json([
                'message' => 'Attendance cannot be submitted before the session date.'
            ], 400); 
        }

        $studentIds = $request->input('student_ids');
        StudentAttendance::where('session_cycle_id', $id)
            ->whereIn('user_id', $studentIds)
            ->update(['attendance' => 1]);
        SessionCycle::where('id', $id)->update(['students_attendance' => 1]);

        return response()->json(['message' => 'Attendance updated successfully'], 200);
    }

    /**
     * @OA\Post(
     *      path="/student_grade/{id}",
     *      operationId="student_grade",
     *      tags={"Instructor - Courses"},
     *      summary="Student Grade",
     *      description="Sending Course Grade for Student",
     *      security={ {"sanctum": {} }},
     *      @OA\Parameter(
     *          name="id",
     *          description="Course id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(
     *              mediaType="multipart/form-data",
     *              @OA\Schema(
     *                  required={"user_id", "grade"},
     *                  @OA\Property(
     *                      property="user_id",
     *                      type="integer",
     *                      description="student ID"
     *                  ),
     *                  @OA\Property(
     *                      property="grade",
     *                      type="integer",
     *                      description="grade for student"
     *                  ),
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\MediaType(
     *           mediaType="application/json",
     *      )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthenticated",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden"
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="not found"
     *      ),
     * )
     */
    public function student_grade(SendStudentGrade $request, $id)
    {
        Enrollment::where('course_cycle_id', $id)
            ->where('user_id', $request->input('user_id'))
            ->update(['grade' => $request->input('grade')]);
        return response()->json(['message' => 'Grade Sent Successfully'], 200);
    }

}
