<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class InstructorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $previousMonthStart = Carbon::now()->subMonth()->startOfMonth()->format('Y-m-d');
        $previousMonth = Carbon::now()->subMonth()->startOfMonth()->format('Y-m');

        $work_hours = \DB::table('instructor_salaries')
            ->where('instructor_id', $this->id)
            ->whereDate('month', $currentMonth)
            ->first();
        $salary = \DB::table('instructor_salaries')
            ->where('instructor_id', $this->id)
            ->whereDate('month', $previousMonthStart)
            ->first();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'password' => $this->password,
            'phone' => $this->phone,
            'image' => asset('https://admin.mtechsquare.com/images/instructors/' . $this->profile_img),
            'date_of_birth' => $this->date_of_birth,
            'city' => $this->city,
            'address' => $this->address,
            'gender' => $this->gender,
            'date_of_join' => $this->date_of_join,
            // 'salary' => $salary.'For month:'.$previousMonthStart,
            'month' => $previousMonth,
            'salary' => $salary ? $salary->salary : 0,
            'job_type' => $this->job_type,
            'work_hours' => $work_hours ? $work_hours->work_hours : 0,
            'status' => $this->status,
        ];
    }
}
