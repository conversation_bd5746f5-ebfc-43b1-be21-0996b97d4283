<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_cycles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->date('start_date');
            $table->date('end_date');
            $table->string('day');
            $table->string('time');
            $table->enum('room', ['room1', 'room2'])->nullable();
            $table->enum('course_status', ['not_started', 'in_progress', 'completed','on_hold']);
            $table->foreignId('instructor_id')->nullable()->constrained()->cascadeOnUpdate()->nullOnDelete();
            $table->foreignId('level_id')->nullable()->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->foreignId('cycle_id')->nullable()->constrained()->cascadeOnUpdate()->cascadeOnDelete();
            $table->enum('type', ['online', 'offline']);
            $table->enum('place', ['academy', 'pms'])->nullable();
            $table->boolean('hezb')->default(0)->nullable();
            $table->timestamps();
            $table->unique(['start_date', 'end_date', 'instructor_id', 'day', 'time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_cycles');
    }
};
