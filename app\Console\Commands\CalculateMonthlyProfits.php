<?php

namespace App\Console\Commands;

use App\Models\CashIn;
use App\Models\CashOut;
use App\Models\FinanceCategory;
use App\Models\Profit;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CalculateMonthlyProfits extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calculate:monthly-profits';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate the profits for the previous month and store them in the profits table.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $previousMonth = Carbon::now()->subMonth();
        $startOfMonth = $previousMonth->startOfMonth()->toDateTimeString();
        $endOfMonth = $previousMonth->endOfMonth()->toDateTimeString();

        $totalCashIn = CashIn::whereBetween('date', [$startOfMonth, $endOfMonth])->sum('amount');
        $totalCashOut = CashOut::whereBetween('date', [$startOfMonth, $endOfMonth])->sum('amount');
        $totalProfit = $totalCashIn - $totalCashOut;

        if ($totalProfit != 0) {
            Profit::updateOrCreate([
                'name' => 'total',
                'amount' => $totalProfit,
                'date' => $previousMonth->firstOfMonth()->toDateString(),
            ]);
        }

        $categories = FinanceCategory::with('finance_sub_categories')->get();
        foreach ($categories as $category) {
            $categoryProfit = $this->calculateProfitForCategory($category->id, $startOfMonth, $endOfMonth);
           
            if ($categoryProfit != 0) {
                Profit::updateOrCreate([
                    'name' => $category->name,
                    'amount' => $categoryProfit,
                    'date' => $previousMonth->firstOfMonth()->toDateString(),
                ]);
            }

            $subCategories = $category->finance_sub_categories;
            foreach ($subCategories as $subCategory) {
                $subCategoryProfit = $this->calculateProfitForSubCategory($subCategory->id, $startOfMonth, $endOfMonth);
               
                if ($subCategoryProfit != 0) {
                    Profit::updateOrCreate([
                        'name' => $subCategory->name,
                        'amount' => $subCategoryProfit,
                        'date' => $previousMonth->firstOfMonth()->toDateString(),
                    ]);
                }
            }
        }

        $this->info('Monthly profits calculated and saved successfully.');
    }

    /**
     * Calculate profit for a given category (excluding subcategories).
     *
     * @param int $categoryId
     * @param string $startOfMonth
     * @param string $endOfMonth
     * @return float
     */
    private function calculateProfitForCategory(int $categoryId, string $startOfMonth, string $endOfMonth): float
    {
        $cashIn = CashIn::where('finance_category_id', $categoryId)
            // ->whereNull('finance_sub_category_id') // Exclude subcategory amounts
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->sum('amount');

        $cashOut = CashOut::where('finance_category_id', $categoryId)
            // ->whereNull('finance_sub_category_id') // Exclude subcategory amounts
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->sum('amount');

        return $cashIn - $cashOut;
    }

    /**
     * Calculate profit for a given subcategory.
     *
     * @param int $subCategoryId
     * @param string $startOfMonth
     * @param string $endOfMonth
     * @return float
     */
    private function calculateProfitForSubCategory(int $subCategoryId, string $startOfMonth, string $endOfMonth): float
    {
        $cashIn = CashIn::where('finance_sub_category_id', $subCategoryId)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->sum('amount');

        $cashOut = CashOut::where('finance_sub_category_id', $subCategoryId)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->sum('amount');

        return $cashIn - $cashOut;
    }
}
