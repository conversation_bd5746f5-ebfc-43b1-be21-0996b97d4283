<?php

namespace App\Console\Commands;

use App\Models\CourseCycle;
use App\Models\Enrollment;
use App\Models\User;
use Illuminate\Console\Command;

class UpdateStudentStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'student:updatestatus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update student statuses based on their enrollment in current course cycles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $currentCourseCycles = CourseCycle::whereIn('course_status', ['not_started', 'in_progress'])->pluck('id')->toArray();

        $enrolledStudentIds = Enrollment::whereIn('course_cycle_id', $currentCourseCycles)->pluck('user_id')->toArray();

        User::whereIn('id', $enrolledStudentIds)->update(['status' => 1]);

        User::whereNotIn('id', $enrolledStudentIds)->update(['status' => 0]);

        $this->info('Student statuses have been updated successfully.');
    }
}
