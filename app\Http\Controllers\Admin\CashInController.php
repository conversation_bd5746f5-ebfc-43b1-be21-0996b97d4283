<?php


namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CashInRequest;
use App\Models\CashIn;
use App\Models\FinanceCategory;
use App\Models\FinanceSubCategory;
use Illuminate\Http\Request;

class CashInController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $search = $request->input('search');
        $category = $request->input('category');
        $subCategory = $request->input('sub_category');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $cash_ins = CashIn::orderBy('date', 'desc');

        // Apply search filter
        if ($search) {
            $cash_ins = $cash_ins->where(function ($query) use ($search) {
                $query->where('amount', 'like', '%' . $search . '%')
                    ->orWhere('date', 'like', '%' . $search . '%')
                    ->orWhere('note', 'like', '%' . $search . '%');
            });
        }

        // Apply category filter
        if ($category) {
            $cash_ins = $cash_ins->where('finance_category_id', $category);
        }

        // Apply sub-category filter
        if ($subCategory) {
            $cash_ins = $cash_ins->where('finance_sub_category_id', $subCategory);
        }

        // Apply date filter
        if ($startDate && $endDate) {
            $cash_ins = $cash_ins->whereBetween('date', [$startDate, $endDate]);
        }

        confirmDelete('Delete Cash-In!', "Are you sure you want to delete this cash-in?");

        return view('cash_ins.index', [
            'cash_ins' => $cash_ins->paginate(15)->appends(request()->except('page')),
            'search' => $search,
            'category' => $category,
            'subCategory' => $subCategory,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'categories' => FinanceCategory::whereIn('type', ['both', 'cash_in'])->get(),
            'subCategories' => FinanceSubCategory::whereHas('finance_category', function ($query) {
                $query->whereIn('type', ['both', 'cash_in']);
            })->get(),
        ]);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $financeCategories = FinanceCategory::whereIn('type', ['both', 'cash_in'])->get();
        $financeSubCategories = FinanceSubCategory::all();
        return view('cash_ins.create', compact('financeCategories', 'financeSubCategories'));
    }

    public function getSubCategories($categoryId)
    {
        // Fetch subcategories that belong to the selected category
        $subCategories = FinanceSubCategory::where('finance_category_id', $categoryId)->get();
        return response()->json($subCategories);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CashInRequest $request)
    {
        CashIn::create($request->except('_token'));
        return redirect()->route('cash_ins.index')->with('add', 'Cash-In Created Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(CashIn $cashIn)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CashIn $cashIn)
    {
        $financeCategories = FinanceCategory::whereIn('type', ['both', 'cash_in'])->get();
        $financeSubCategories = FinanceSubCategory::all();
        return view('cash_ins.edit', compact('cashIn', 'financeCategories', 'financeSubCategories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(
        CashInRequest
        $request,
        CashIn $cashIn
    ) {
        $cashIn->update($request->except('_token', '_method'));
        return redirect()->route('cash_ins.index')->with('update', 'Cash-In Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CashIn $cashIn)
    {
        $cashIn->delete();
        return redirect()->route('cash_ins.index')->with('delete', 'Cash-In Deleted Successfully');
    }
}
