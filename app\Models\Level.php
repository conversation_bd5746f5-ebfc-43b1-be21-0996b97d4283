<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Level extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'course_id',
        'course_price',
        'material_price',
    ];

    public static function rules($id = null)
    {
        return [
            'name' => 'required|string|max:255|unique:levels,name,' . $id . ',id,course_id,' . request('course_id'),
            'course_id' => 'required|exists:courses,id',
            'course_price' => 'nullable|numeric',
            'material_price' => 'nullable|numeric',
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($level) {
            // Create 8 sessions for the newly created level
            $sessionsData = [];
            for ($i = 1; $i <= 8; $i++) {
                $sessionsData[] = [
                    'name' => 'Session ' . $i,
                    'level_id' => $level->id,                    
                ];
            }

            DB::table('sessions')->insert($sessionsData);
        });
    }

    public function course_cycles()
    {
        return $this->hasMany(CourseCycle::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }
    public function sessions()
    {
        return $this->hasMany(Session::class);
    }
}
