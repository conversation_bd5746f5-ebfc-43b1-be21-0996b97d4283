<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use App\Http\Requests\Admin\CycleRequest;
use App\Http\Requests\Admin\UpdateCycleRequest;
use App\Models\Cycle;
use Illuminate\Http\Request;

class CycleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $cycles = Cycle::orderBy('created_at', 'desc');

        if ($search) {
            $cycles = $cycles
                ->where('name', 'like', '%' . request()->get('search', '') . '%');
        }

        confirmDelete('Delete Cycle!', "Are you sure you want to delete this cycle?");

        return view('cycles.index', [
            'cycles' => $cycles->paginate(12)->appends(request()->except('page')),
            'search' => $search,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cycles.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CycleRequest $request)
    {
        Cycle::create($request->except('_token'));
        return redirect()->route('cycles.index')->with('add', 'Cycle Added Successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Cycle $cycle)
    {
        return view('cycles.show', compact('cycle'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Cycle $cycle)
    {
        return view('cycles.edit', compact('cycle'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCycleRequest $request, Cycle $cycle)
    {
        $cycle->update($request->except('_token', '_method'));
        return redirect()->route('cycles.index')->with('update', 'Cycle Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Cycle $cycle)
    {
        $cycle->delete();
        return redirect()->route('cycles.index')->with('delete', 'Cycle Deleted Successfully');
    }
}
