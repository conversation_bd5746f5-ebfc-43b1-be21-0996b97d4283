<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\SessionCycle;
use Carbon\Carbon;

class UpdateSessionCycleDatesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        $sessionCycle = $this->route('sessionCycle'); 
        $courseCycle = $sessionCycle->courseCycle;

        $previousSession = $courseCycle->sessionCycles()
            ->where('date', '<', $sessionCycle->date)
            ->orderBy('date', 'desc')
            ->first();

        $nextSession = $courseCycle->sessionCycles()
            ->where('date', '>', $sessionCycle->date)
            ->orderBy('date', 'asc')
            ->first();

        return [
            'date_' . $sessionCycle->id => [
                'required',
                'date',
                function ($attribute, $value, $fail) use ($previousSession, $nextSession) {
                    $newDate = Carbon::parse($value);

                    if ($previousSession && $newDate->lt(Carbon::parse($previousSession->date))) {
                        $fail('The new session date cannot be before the previous session\'s date (' . Carbon::parse($previousSession->date)->format('d-m-Y') . ').');
                    }

                    if ($nextSession && $newDate->gt(Carbon::parse($nextSession->date))) {
                        $fail('The new session date cannot be after the next session\'s date (' . Carbon::parse($nextSession->date)->format('d-m-Y') . ').');
                    }
                },
            ],
        ];
    }
}
