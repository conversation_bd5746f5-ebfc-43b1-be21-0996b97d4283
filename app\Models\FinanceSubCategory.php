<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FinanceSubCategory extends Model
{
    use HasFactory;

    public function finance_category()
    {
        return $this->belongsTo(FinanceCategory::class);
    }
    public function cash_ins()
    {
        return $this->hasMany(CashIn::class);
    }
    public function cash_outs()
    {
        return $this->hasMany(CashOut::class);
    }

}
