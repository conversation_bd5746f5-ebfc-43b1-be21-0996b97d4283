<?php

namespace App\Exports;

use App\Models\CashIn;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class CashInExport implements FromCollection, WithHeadings, WithTitle
{
    protected $startDate;
    protected $endDate;
    protected $category;
    protected $subCategory;

    public function __construct($startDate = null, $endDate = null, $category = null, $subCategory = null)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->category = $category;
        $this->subCategory = $subCategory;
    }

    public function collection()
    {
        $query = CashIn::query();

        if ($this->startDate) {
            $query->whereDate('date', '>=', $this->startDate);
        }

        if ($this->endDate) {
            $query->whereDate('date', '<=', $this->endDate);
        }

        if ($this->category) {
            $query->where('finance_category_id', $this->category);
        }

        if ($this->subCategory) {
            $query->where('finance_sub_category_id', $this->subCategory);
        }

        $cashIns = $query->orderBy('date', 'desc')->get();

        // Map the results to the desired format
        return $cashIns->map(function ($cashIn, $index) {
            return [
                '#' => $index + 1,
                'Category' => $cashIn->finance_category->name ?? '--',
                'Sub-Category' => $cashIn->finance_sub_category->name ?? '--',
                'Date' => Carbon::parse($cashIn->date)->format('d-m-Y'),
                'Amount' => $cashIn->amount, 
                'Payment-Way' => $cashIn->payment_way?? '--',
                'Notes' => $cashIn->note ?? '--',
            ];
        });
    }

    public function headings(): array
    {
        return [
            '#',
            'Category',
            'Sub-Category',
            'Date',
            'Amount',
            'Payment-Way',
            'Notes',
        ];
    }

    public function title(): string
    {
        $dateRange = '';

        if ($this->startDate) {
            $dateRange = Carbon::parse($this->startDate)->format('d-m-Y');
        }

        if ($this->endDate) {
            $dateRange .= ' to ' . Carbon::parse($this->endDate)->format('d-m-Y');
        }

        return 'CashIns ' . $dateRange;
    }
}
