<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CourseCycle;
use App\Models\InstructorAttendance;
use App\Models\InstructorSalary;
use App\Models\SessionCycle;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class InstructorAttendanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $search = request()->input('search');
        $today = Carbon::today();
        $startOfMonth = Carbon::now()->startOfMonth()->toDateString();
        $courseCyclesInProgress = CourseCycle::whereIn('course_status', ['in_progress', 'completed'])
        ->pluck('id');    

        $todaySessionCycles = SessionCycle::whereIn('course_cycle_id', $courseCyclesInProgress)
            // ->whereDate('date', $today)
            ->whereBetween('date', [$startOfMonth, $today])
            ->pluck('id');

        $unsubmittedSessions = SessionCycle::with(['courseCycle', 'instructor_attendances'])
            ->whereIn('id', $todaySessionCycles)
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('instructor_attendances')
                    ->whereColumn('instructor_attendances.session_cycle_id', 'session_cycles.id');
            });

        if ($search) {
            $unsubmittedSessions = $unsubmittedSessions
                ->whereAny(['session_cycle_id'], 'like', '%' . request()->get('search', '') . '%');
        }

        return view('course_cycles.attendance', [
            'unsubmittedSessions' => $unsubmittedSessions->paginate(20)->appends(request()->except('page')),
            'search' => $search,
        ]);

    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $data = $request->except('_token');
        $sessionCycleId = $request->input('session_cycle_id');
        $instructorIds = $data['instructor_id'];
        // $currentMonth = now()->startOfMonth();
        // foreach ($instructorIds as $index => $instructorId) {
        //     InstructorAttendance::create([
        //         'session_cycle_id' => $sessionCycleId,
        //         'instructor_id' => $instructorId,
        //     ]);
        //     InstructorSalary::updateOrCreate(
        //         ['instructor_id' => $instructorId, 'month' => $currentMonth], 
        //         ['work_hours' => DB::raw('work_hours + 2')] 
        //     );
        // }
        $existingAttendances = InstructorAttendance::where('session_cycle_id', $sessionCycleId)->get();

        // Iterate through submitted instructor IDs
        foreach ($instructorIds as $index => $newInstructorId) {
            // Check if this instructor already has an attendance record for the session
            $existingAttendance = $existingAttendances->get($index);

            if ($existingAttendance) {
                // If the instructor ID is different, update the record
                if ($existingAttendance->instructor_id != $newInstructorId) {
                    if (
                        !InstructorAttendance::where('session_cycle_id', $sessionCycleId)
                            ->where('instructor_id', $newInstructorId)
                            ->exists()
                    ) {
                        $this->adjustWorkHours($existingAttendance->instructor_id, -2);
                        // Update the record with the new instructor ID
                        $existingAttendance->update([
                            'instructor_id' => $newInstructorId,
                        ]);

                        // Adjust work_hours for both the old and new instructor
                        // Deduct 2 hours from the old instructor
                        $this->adjustWorkHours($newInstructorId, hours: 2);  // Add 2 hours to the new instructor
                    }
                }
            } else {
                if (
                    !InstructorAttendance::where('session_cycle_id', $sessionCycleId)
                        ->where('instructor_id', $newInstructorId)
                        ->exists()
                ) {
                    InstructorAttendance::create([
                        'session_cycle_id' => $sessionCycleId,
                        'instructor_id' => $newInstructorId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    // Add work hours for the new instructor
                    $this->adjustWorkHours($newInstructorId, 2);
                }
            }
        }
        return redirect()->back()->with('add', 'Instructor Attendance Saved Successfully');
    }

    private function adjustWorkHours($instructorId, $hours)
    {
        // Get the current month and year
        $currentMonthStart = Carbon::now()->startOfMonth()->format('Y-m-d');


        // Find or create the salary record for the instructor
        $salaryRecord = InstructorSalary::updateOrCreate(
            [
                'instructor_id' => $instructorId,
                'month' => $currentMonthStart,
            ],
            [
                'work_hours' => DB::raw("work_hours + $hours"), // Adjust work hours by adding or subtracting hours
            ]
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(InstructorAttendance $instructorAttendance)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(InstructorAttendance $instructorAttendance)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, InstructorAttendance $instructorAttendance)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(InstructorAttendance $instructorAttendance)
    {
        //
    }
}
